
-- Create progress_tracking table for tracking participant progress
CREATE TABLE public.progress_tracking (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  current_stage TEXT NOT NULL DEFAULT 'registration',
  stage_status TEXT NOT NULL DEFAULT 'pending',
  registration_date TIMESTAMP WITH TIME ZONE,
  interview_date TIMESTAMP WITH TIME ZONE,
  account_created_date TIMESTAMP WITH TIME ZONE,
  exam_payment_date TIMESTAMP WITH TIME ZONE,
  exam_scheduled_date TIMESTAMP WITH TIME ZONE,
  reregistration_payment_date TIMESTAMP WITH TIME ZONE,
  active_participant_date TIMESTAMP WITH TIME ZONE,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(user_id)
);

-- Enable Row Level Security
ALTER TABLE public.progress_tracking ENABLE ROW LEVEL SECURITY;

-- Create policies for progress_tracking
CREATE POLICY "Users can view own progress" ON public.progress_tracking
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all progress" ON public.progress_tracking
  FOR SELECT USING (true);

CREATE POLICY "Admins can update all progress" ON public.progress_tracking
  FOR UPDATE USING (true);

CREATE POLICY "Admins can insert progress" ON public.progress_tracking
  FOR INSERT WITH CHECK (true);

-- Create function to calculate progress percentage
CREATE OR REPLACE FUNCTION public.calculate_progress_percentage(user_uuid UUID)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  progress_count INTEGER := 0;
  total_stages INTEGER := 7;
  progress_record RECORD;
BEGIN
  SELECT * INTO progress_record FROM public.progress_tracking WHERE user_id = user_uuid;
  
  IF progress_record IS NULL THEN
    RETURN 0;
  END IF;
  
  -- Count completed stages
  IF progress_record.registration_date IS NOT NULL THEN
    progress_count := progress_count + 1;
  END IF;
  
  IF progress_record.interview_date IS NOT NULL THEN
    progress_count := progress_count + 1;
  END IF;
  
  IF progress_record.account_created_date IS NOT NULL THEN
    progress_count := progress_count + 1;
  END IF;
  
  IF progress_record.exam_payment_date IS NOT NULL THEN
    progress_count := progress_count + 1;
  END IF;
  
  IF progress_record.exam_scheduled_date IS NOT NULL THEN
    progress_count := progress_count + 1;
  END IF;
  
  IF progress_record.reregistration_payment_date IS NOT NULL THEN
    progress_count := progress_count + 1;
  END IF;
  
  IF progress_record.active_participant_date IS NOT NULL THEN
    progress_count := progress_count + 1;
  END IF;
  
  RETURN ROUND((progress_count::DECIMAL / total_stages) * 100);
END;
$$;

-- Create trigger function to handle new program registration
CREATE OR REPLACE FUNCTION public.handle_new_program_registration()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = ''
AS $$
BEGIN
  -- Create progress tracking record for new program registration
  INSERT INTO public.progress_tracking (
    user_id,
    current_stage,
    stage_status,
    registration_date
  )
  VALUES (
    NEW.user_id,
    'registration',
    'completed',
    NEW.created_at
  )
  ON CONFLICT (user_id) DO UPDATE SET
    registration_date = COALESCE(public.progress_tracking.registration_date, NEW.created_at),
    updated_at = NOW();

  RETURN NEW;
END;
$$;

-- Create trigger to automatically create progress tracking on program registration
CREATE TRIGGER on_program_registration_created
  AFTER INSERT ON public.program_registrations
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_program_registration();

-- Add sample data for existing users
INSERT INTO public.progress_tracking (user_id, current_stage, stage_status, registration_date, interview_date, account_created_date, exam_payment_date)
SELECT
  auth.users.id,
  'exam_scheduled',
  'completed',
  NOW() - INTERVAL '30 days',
  NOW() - INTERVAL '25 days',
  NOW() - INTERVAL '23 days',
  NOW() - INTERVAL '20 days'
FROM auth.users
WHERE auth.users.id NOT IN (SELECT user_id FROM public.progress_tracking);
