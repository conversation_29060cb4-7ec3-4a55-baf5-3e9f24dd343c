
import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CalendarDays, CreditCard, FileText, CheckCircle, Clock, Upload, RefreshCw } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';
import PaymentModal from '../PaymentModal';
import { useDataCache } from '@/hooks/useDataCache';
import { ParticipantDashboardSkeleton } from '@/components/ui/dashboard-skeleton';

interface ProgressData {
  id: string;
  current_stage: string;
  stage_status: string;
  registration_date: string | null;
  interview_date: string | null;
  account_created_date: string | null;
  exam_payment_date: string | null;
  exam_scheduled_date: string | null;
  reregistration_payment_date: string | null;
  active_participant_date: string | null;
  notes: string | null;
}

interface ProfileData {
  full_name: string | null;
  email: string | null;
  phone: string | null;
}

const ParticipantDashboard = () => {
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const { user, session } = useAuth();

  // Use cached data fetching
  const fetchParticipantData = useCallback(async () => {
    if (!session?.user?.id) {
      throw new Error('User not authenticated');
    }

    console.log('Fetching participant data...');

    // Fetch progress data
    const { data: progress, error: progressError } = await supabase
      .from('progress_tracking')
      .select('*')
      .eq('user_id', session.user.id)
      .single();

    if (progressError && progressError.code !== 'PGRST116') {
      console.error('Error fetching progress:', progressError);
    }

    // Fetch profile data
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', session.user.id)
      .single();

    if (profileError && profileError.code !== 'PGRST116') {
      console.error('Error fetching profile:', profileError);
    }

    // Calculate progress percentage
    let percentage = 0;
    if (progress) {
      const { data: percentageData } = await supabase
        .rpc('calculate_progress_percentage', { user_uuid: session.user.id });
      percentage = percentageData || 0;
    }

    return {
      progress,
      profile,
      percentage
    };
  }, [session?.user?.id]);

  const {
    data: participantData,
    loading,
    error,
    isStale,
    refresh
  } = useDataCache(
    `participant-${session?.user?.id}`,
    fetchParticipantData,
    {
      ttl: 30 * 1000, // 30 seconds cache for faster updates
      staleWhileRevalidate: true
    }
  );

  const progressData = participantData?.progress;
  const profileData = participantData?.profile;
  const progressPercentage = participantData?.percentage || 0;

  const getStageTitle = (stage: string) => {
    const stageMap: { [key: string]: string } = {
      'registration': 'Pendaftaran',
      'interview': 'Wawancara',
      'account_created': 'Akun Dibuat',
      'exam_payment': 'Pembayaran Ujian',
      'exam_scheduled': 'Ujian Terjadwal',
      'reregistration_payment': 'Pembayaran Ulang',
      'active_participant': 'Peserta Aktif'
    };
    return stageMap[stage] || stage;
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">Selesai</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Menunggu</Badge>;
      case 'current':
        return <Badge className="bg-blue-100 text-blue-800">Sedang Berlangsung</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const timeline = [
    { 
      stage: 'Pendaftaran', 
      status: progressData?.registration_date ? 'completed' : 'pending', 
      date: progressData?.registration_date ? new Date(progressData.registration_date).toLocaleDateString('id-ID') : 'TBD' 
    },
    { 
      stage: 'Wawancara', 
      status: progressData?.interview_date ? 'completed' : progressData?.current_stage === 'interview' ? 'current' : 'pending', 
      date: progressData?.interview_date ? new Date(progressData.interview_date).toLocaleDateString('id-ID') : 'TBD' 
    },
    { 
      stage: 'Akun Dibuat', 
      status: progressData?.account_created_date ? 'completed' : progressData?.current_stage === 'account_created' ? 'current' : 'pending', 
      date: progressData?.account_created_date ? new Date(progressData.account_created_date).toLocaleDateString('id-ID') : 'TBD' 
    },
    { 
      stage: 'Pembayaran Ujian', 
      status: progressData?.exam_payment_date ? 'completed' : progressData?.current_stage === 'exam_payment' ? 'current' : 'pending', 
      date: progressData?.exam_payment_date ? new Date(progressData.exam_payment_date).toLocaleDateString('id-ID') : 'TBD' 
    },
    { 
      stage: 'Ujian Terjadwal', 
      status: progressData?.exam_scheduled_date ? 'completed' : progressData?.current_stage === 'exam_scheduled' ? 'current' : 'pending', 
      date: progressData?.exam_scheduled_date ? new Date(progressData.exam_scheduled_date).toLocaleDateString('id-ID') : 'TBD' 
    },
    { 
      stage: 'Pembayaran Daftar Ulang', 
      status: progressData?.reregistration_payment_date ? 'completed' : progressData?.current_stage === 'reregistration_payment' ? 'current' : 'pending', 
      date: progressData?.reregistration_payment_date ? new Date(progressData.reregistration_payment_date).toLocaleDateString('id-ID') : 'TBD' 
    },
    { 
      stage: 'Peserta Aktif', 
      status: progressData?.active_participant_date ? 'completed' : progressData?.current_stage === 'active_participant' ? 'current' : 'pending', 
      date: progressData?.active_participant_date ? new Date(progressData.active_participant_date).toLocaleDateString('id-ID') : 'TBD' 
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500';
      case 'current': return 'bg-blue-500';
      default: return 'bg-gray-300';
    }
  };

  if (loading && !participantData) {
    return <ParticipantDashboardSkeleton />;
  }

  const displayName = profileData?.full_name || user?.name || user?.email || 'Peserta';
  const currentStageTitle = progressData ? getStageTitle(progressData.current_stage) : 'Pendaftaran';

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Selamat datang kembali, {displayName}</h1>
          <p className="text-gray-600">Pantau progress dan kelola perjalanan belajar Anda</p>
          <div className="mt-2 flex items-center gap-2">
            <Button
              onClick={refresh}
              variant="outline"
              size="sm"
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
              Refresh Data
            </Button>
            {isStale && (
              <Badge variant="outline" className="text-orange-600">
                Data mungkin tidak terbaru
              </Badge>
            )}
          </div>
        </div>

        {/* Status Overview */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CheckCircle className="h-5 w-5 mr-2 text-green-500" />
                Status Saat Ini
              </CardTitle>
            </CardHeader>
            <CardContent>
              {getStatusBadge(progressData?.stage_status || 'pending')}
              <p className="text-sm text-gray-600 mt-2">Tahap: {currentStageTitle}</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CalendarDays className="h-5 w-5 mr-2 text-blue-500" />
                Ujian Mendatang
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-lg font-semibold">
                {progressData?.exam_scheduled_date 
                  ? new Date(progressData.exam_scheduled_date).toLocaleDateString('id-ID')
                  : 'Belum dijadwalkan'
                }
              </p>
              <p className="text-sm text-gray-600">Pastikan Anda sudah siap!</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CreditCard className="h-5 w-5 mr-2 text-green-500" />
                Status Pembayaran
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Badge className={progressData?.exam_payment_date ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"}>
                {progressData?.exam_payment_date ? 'Sudah Dibayar' : 'Belum Dibayar'}
              </Badge>
              <p className="text-sm text-gray-600 mt-2">
                {progressData?.exam_payment_date ? 'Biaya ujian telah diproses' : 'Menunggu pembayaran'}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Progress Tracker */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Pelacak Progress</CardTitle>
            <CardDescription>Perjalanan Anda dari pendaftaran hingga menjadi peserta aktif</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between text-sm">
                <span>Progress Keseluruhan</span>
                <span>{progressPercentage}%</span>
              </div>
              <Progress value={progressPercentage} className="w-full" />
              
              <div className="grid gap-4 mt-6">
                {timeline.map((item, index) => (
                  <div key={index} className="flex items-center space-x-4">
                    <div className={`w-4 h-4 rounded-full ${getStatusColor(item.status)}`} />
                    <div className="flex-1">
                      <p className="font-medium">{item.stage}</p>
                      <p className="text-sm text-gray-500">{item.date}</p>
                    </div>
                    {item.status === 'completed' && (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    )}
                    {item.status === 'current' && (
                      <Clock className="h-5 w-5 text-blue-500" />
                    )}
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tabs for detailed info */}
        <Tabs defaultValue="payments" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="payments">Riwayat Pembayaran</TabsTrigger>
            <TabsTrigger value="documents">Dokumen</TabsTrigger>
            <TabsTrigger value="profile">Profil</TabsTrigger>
          </TabsList>

          <TabsContent value="payments">
            <Card>
              <CardHeader>
                <CardTitle>Riwayat Pembayaran</CardTitle>
                <CardDescription>Lihat semua transaksi pembayaran Anda</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {progressData?.exam_payment_date && (
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <p className="font-medium">Biaya Ujian</p>
                        <p className="text-sm text-gray-600">Rp 300.000</p>
                        <p className="text-sm text-gray-500">
                          {new Date(progressData.exam_payment_date).toLocaleDateString('id-ID')} • Transfer Bank
                        </p>
                      </div>
                      <Badge className="bg-green-100 text-green-800">Sudah Dibayar</Badge>
                    </div>
                  )}
                  
                  <div className="pt-4 border-t">
                    <Button 
                      onClick={() => setShowPaymentModal(true)}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      Bayar Biaya Daftar Ulang
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents">
            <Card>
              <CardHeader>
                <CardTitle>Dokumen & Sertifikat</CardTitle>
                <CardDescription>Upload dokumen yang diperlukan dan unduh sertifikat</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 mb-2">Upload bukti pembayaran atau dokumen lainnya</p>
                    <Button variant="outline">Pilih File</Button>
                  </div>
                  
                  <div className="space-y-2">
                    {progressData?.exam_payment_date && (
                      <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                        <div className="flex items-center">
                          <FileText className="h-5 w-5 text-blue-500 mr-2" />
                          <span>Kwitansi Pembayaran - Biaya Ujian</span>
                        </div>
                        <Button variant="outline" size="sm">Unduh</Button>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="profile">
            <Card>
              <CardHeader>
                <CardTitle>Informasi Profil</CardTitle>
                <CardDescription>Kelola detail akun Anda</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium">Nama Lengkap</label>
                      <p className="mt-1 text-gray-900">{profileData?.full_name || 'Tidak tersedia'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Email</label>
                      <p className="mt-1 text-gray-900">{profileData?.email || user?.email}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Nomor Telepon</label>
                      <p className="mt-1 text-gray-900">{profileData?.phone || 'Tidak tersedia'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Status Saat Ini</label>
                      <p className="mt-1 text-gray-900">{currentStageTitle}</p>
                    </div>
                  </div>
                  <Button variant="outline" className="mt-4">Edit Profil</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      <PaymentModal 
        isOpen={showPaymentModal} 
        onClose={() => setShowPaymentModal(false)} 
      />
    </div>
  );
};

export default ParticipantDashboard;
