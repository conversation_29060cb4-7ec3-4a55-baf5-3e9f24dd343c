
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alog<PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Upload, CreditCard, Building2, Smartphone } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const PaymentModal = ({ isOpen, onClose }: PaymentModalProps) => {
  const { toast } = useToast();
  const [selectedMethod, setSelectedMethod] = useState('');
  const [paymentProof, setPaymentProof] = useState<File | null>(null);

  const paymentMethods = [
    {
      id: 'bank_transfer',
      name: 'Bank Transfer',
      icon: Building2,
      details: {
        bank: 'Bank Mandiri',
        account: '**********',
        name: 'CSEL UI Program'
      }
    },
    {
      id: 'e_wallet',
      name: 'E-Wallet',
      icon: Smartphone,
      details: {
        service: 'GoPay / OVO / DANA',
        number: '************',
        name: 'CSEL UI'
      }
    },
    {
      id: 'credit_card',
      name: 'Credit Card',
      icon: CreditCard,
      details: {
        note: 'Secure online payment processing'
      }
    }
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    toast({
      title: "Payment Submitted!",
      description: "Your payment proof has been uploaded and is being verified.",
    });
    
    onClose();
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setPaymentProof(file);
      console.log('Payment proof uploaded:', file.name);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl text-blue-900">Re-registration Payment</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Payment Amount */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Payment Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between items-center text-lg">
                <span>Re-registration Fee:</span>
                <span className="font-bold text-blue-600">Rp 300,000</span>
              </div>
            </CardContent>
          </Card>

          {/* Payment Methods */}
          <div>
            <Label className="text-base font-medium mb-4 block">Select Payment Method</Label>
            <div className="grid gap-4">
              {paymentMethods.map((method) => (
                <Card 
                  key={method.id}
                  className={`cursor-pointer transition-colors ${
                    selectedMethod === method.id ? 'border-blue-500 bg-blue-50' : 'hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedMethod(method.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <method.icon className="h-6 w-6 text-blue-600" />
                      <div className="flex-1">
                        <p className="font-medium">{method.name}</p>
                        {method.details.bank && (
                          <p className="text-sm text-gray-600">
                            {method.details.bank} - {method.details.account}
                          </p>
                        )}
                        {method.details.service && (
                          <p className="text-sm text-gray-600">{method.details.service}</p>
                        )}
                        {method.details.note && (
                          <p className="text-sm text-gray-600">{method.details.note}</p>
                        )}
                      </div>
                      <div className={`w-4 h-4 rounded-full border-2 ${
                        selectedMethod === method.id ? 'bg-blue-500 border-blue-500' : 'border-gray-300'
                      }`} />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Payment Instructions */}
          {selectedMethod && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Payment Instructions</CardTitle>
              </CardHeader>
              <CardContent>
                {selectedMethod === 'bank_transfer' && (
                  <div className="space-y-2 text-sm">
                    <p>1. Transfer Rp 300,000 to the account above</p>
                    <p>2. Include your full name in the transfer description</p>
                    <p>3. Take a screenshot of the transfer confirmation</p>
                    <p>4. Upload the proof below</p>
                  </div>
                )}
                {selectedMethod === 'e_wallet' && (
                  <div className="space-y-2 text-sm">
                    <p>1. Send Rp 300,000 to the number above</p>
                    <p>2. Add a note with your full name</p>
                    <p>3. Take a screenshot of the transaction</p>
                    <p>4. Upload the proof below</p>
                  </div>
                )}
                {selectedMethod === 'credit_card' && (
                  <div className="space-y-2 text-sm">
                    <p>1. Click the payment button below</p>
                    <p>2. You'll be redirected to secure payment gateway</p>
                    <p>3. Complete the payment process</p>
                    <p>4. Return here for confirmation</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* File Upload */}
          {selectedMethod && selectedMethod !== 'credit_card' && (
            <div>
              <Label className="text-base font-medium mb-4 block">Upload Payment Proof</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 mb-4">
                  {paymentProof ? `Selected: ${paymentProof.name}` : 'Click to upload payment proof'}
                </p>
                <Input
                  type="file"
                  accept="image/*,.pdf"
                  onChange={handleFileUpload}
                  className="hidden"
                  id="payment-proof"
                />
                <Label htmlFor="payment-proof">
                  <Button variant="outline" type="button">
                    Choose File
                  </Button>
                </Label>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-4 pt-4">
            {selectedMethod === 'credit_card' ? (
              <Button className="flex-1 bg-blue-600 hover:bg-blue-700">
                Proceed to Payment Gateway
              </Button>
            ) : (
              <Button 
                onClick={handleSubmit}
                className="flex-1 bg-blue-600 hover:bg-blue-700"
                disabled={!selectedMethod || !paymentProof}
              >
                Submit Payment Proof
              </Button>
            )}
            <Button type="button" variant="outline" onClick={onClose} className="flex-1">
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PaymentModal;
