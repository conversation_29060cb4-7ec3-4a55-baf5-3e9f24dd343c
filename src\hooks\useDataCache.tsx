import { useState, useEffect, useCallback } from 'react';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiry: number;
}

interface CacheOptions {
  ttl?: number; // Time to live in milliseconds (default: 5 minutes)
  staleWhileRevalidate?: boolean; // Return stale data while fetching new data
}

class DataCache {
  private cache = new Map<string, CacheEntry<any>>();
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes

  set<T>(key: string, data: T, ttl?: number): void {
    const now = Date.now();
    const expiry = now + (ttl || this.DEFAULT_TTL);
    
    this.cache.set(key, {
      data,
      timestamp: now,
      expiry
    });
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    const now = Date.now();
    
    if (now > entry.expiry) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  getStale<T>(key: string): T | null {
    const entry = this.cache.get(key);
    return entry ? entry.data : null;
  }

  isStale(key: string): boolean {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return true;
    }

    return Date.now() > entry.expiry;
  }

  invalidate(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }
}

// Global cache instance
const globalCache = new DataCache();

export function useDataCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: CacheOptions = {}
) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isStale, setIsStale] = useState(false);

  const { ttl, staleWhileRevalidate = true } = options;

  const fetchData = useCallback(async (forceRefresh = false) => {
    try {
      // Check cache first
      if (!forceRefresh) {
        const cachedData = globalCache.get<T>(key);
        if (cachedData) {
          setData(cachedData);
          setIsStale(false);
          return cachedData;
        }

        // If stale-while-revalidate is enabled, return stale data immediately
        if (staleWhileRevalidate) {
          const staleData = globalCache.getStale<T>(key);
          if (staleData) {
            setData(staleData);
            setIsStale(true);
            // Continue to fetch fresh data in background
          }
        }
      }

      setLoading(true);
      setError(null);

      const freshData = await fetcher();
      
      // Cache the fresh data
      globalCache.set(key, freshData, ttl);
      
      setData(freshData);
      setIsStale(false);
      
      return freshData;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      console.error(`Error fetching data for key ${key}:`, err);
      
      // If we have stale data and this is a background refresh, keep the stale data
      if (staleWhileRevalidate && globalCache.getStale<T>(key)) {
        const staleData = globalCache.getStale<T>(key);
        setData(staleData);
        setIsStale(true);
      }
      
      throw err;
    } finally {
      setLoading(false);
    }
  }, [key, fetcher, ttl, staleWhileRevalidate]);

  const invalidate = useCallback(() => {
    globalCache.invalidate(key);
    setData(null);
    setIsStale(false);
  }, [key]);

  const refresh = useCallback(() => {
    return fetchData(true);
  }, [fetchData]);

  // Initial fetch
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    isStale,
    refresh,
    invalidate,
    refetch: fetchData
  };
}

export { globalCache };
