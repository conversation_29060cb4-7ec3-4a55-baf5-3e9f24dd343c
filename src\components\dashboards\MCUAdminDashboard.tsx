import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Users, UserCheck, Clock, CheckCircle, Edit, RefreshCw } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useDataCache } from '@/hooks/useDataCache';
import { AdminDashboardSkeleton } from '@/components/ui/dashboard-skeleton';

interface ProgressTracking {
  current_stage: string;
  stage_status: string;
  registration_date: string | null;
  interview_date: string | null;
  account_created_date: string | null;
  exam_payment_date: string | null;
  exam_scheduled_date: string | null;
  reregistration_payment_date: string | null;
  active_participant_date: string | null;
  notes: string | null;
}

interface ParticipantData {
  user_id: string;
  full_name: string;
  email: string;
  phone: string;
  current_stage: string;
  stage_status: string;
  registration_date: string | null;
  interview_date: string | null;
  account_created_date: string | null;
  exam_payment_date: string | null;
  exam_scheduled_date: string | null;
  reregistration_payment_date: string | null;
  active_participant_date: string | null;
  notes: string | null;
}

const MCUAdminDashboard = () => {
  const [selectedParticipant, setSelectedParticipant] = useState<ParticipantData | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editForm, setEditForm] = useState({
    current_stage: '',
    stage_status: '',
    interview_date: '',
    exam_payment_date: '',
    exam_scheduled_date: '',
    reregistration_payment_date: '',
    active_participant_date: '',
    notes: ''
  });
  const { toast } = useToast();

  // Use cached data fetching
  const fetchParticipantsData = useCallback(async () => {
    console.log('Fetching participants data...');

    // Get participants only (users with role 'participant' and have program registrations)
    const { data: participantRoles, error: rolesError } = await supabase
      .from('user_roles')
      .select('user_id')
      .eq('role', 'participant');

    if (rolesError) {
      console.error('Error fetching participant roles:', rolesError);
      throw new Error('Gagal memuat data roles');
    }

    if (!participantRoles || participantRoles.length === 0) {
      console.log('No participants found');
      return [];
    }

    const participantIds = participantRoles.map(role => role.user_id);
    console.log('Participant IDs:', participantIds);

    // Get profiles for participants only
    const { data: profilesData, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .in('id', participantIds);

    if (profilesError) {
      console.error('Error fetching profiles:', profilesError);
      throw new Error('Gagal memuat data profiles');
    }

    console.log('Profiles data:', profilesData);

    // Get progress tracking data for participants only
    const { data: progressData, error: progressError } = await supabase
      .from('progress_tracking')
      .select('*')
      .in('user_id', participantIds);

    if (progressError) {
      console.error('Error fetching progress tracking:', progressError);
    }

    console.log('Progress tracking data:', progressData);

    // Get program registrations for participants only
    const { data: registrationData, error: registrationError } = await supabase
      .from('program_registrations')
      .select('*')
      .in('user_id', participantIds);

    if (registrationError) {
      console.error('Error fetching program registrations:', registrationError);
    }

    console.log('Program registrations data:', registrationData);

    // Combine the data - only include participants who have program registrations
    const formattedData = profilesData?.filter(profile => {
      // Only include if user has a program registration
      return registrationData?.some(reg => reg.user_id === profile.id);
    }).map(profile => {
      const progressRecord = progressData?.find(p => p.user_id === profile.id);

      return {
        user_id: profile.id,
        full_name: profile.full_name || 'Tidak tersedia',
        email: profile.email || '',
        phone: profile.phone || 'Tidak tersedia',
        current_stage: progressRecord?.current_stage || 'registration',
        stage_status: progressRecord?.stage_status || 'pending',
        registration_date: progressRecord?.registration_date || null,
        interview_date: progressRecord?.interview_date || null,
        account_created_date: progressRecord?.account_created_date || null,
        exam_payment_date: progressRecord?.exam_payment_date || null,
        exam_scheduled_date: progressRecord?.exam_scheduled_date || null,
        reregistration_payment_date: progressRecord?.reregistration_payment_date || null,
        active_participant_date: progressRecord?.active_participant_date || null,
        notes: progressRecord?.notes || null
      };
    }) || [];

    console.log('Formatted participants data:', formattedData);
    return formattedData;
  }, []);

  const {
    data: participants = [],
    loading,
    error,
    isStale,
    refresh
  } = useDataCache('mcu-participants', fetchParticipantsData, {
    ttl: 2 * 60 * 1000, // 2 minutes cache
    staleWhileRevalidate: true
  });

  // Show error toast if there's an error
  useEffect(() => {
    if (error) {
      toast({
        title: "Error",
        description: error,
        variant: "destructive"
      });
    }
  }, [error, toast]);



  const handleEditParticipant = (participant: ParticipantData) => {
    setSelectedParticipant(participant);
    setEditForm({
      current_stage: participant.current_stage,
      stage_status: participant.stage_status,
      interview_date: participant.interview_date ? participant.interview_date.split('T')[0] : '',
      exam_payment_date: participant.exam_payment_date ? participant.exam_payment_date.split('T')[0] : '',
      exam_scheduled_date: participant.exam_scheduled_date ? participant.exam_scheduled_date.split('T')[0] : '',
      reregistration_payment_date: participant.reregistration_payment_date ? participant.reregistration_payment_date.split('T')[0] : '',
      active_participant_date: participant.active_participant_date ? participant.active_participant_date.split('T')[0] : '',
      notes: participant.notes || ''
    });
    setIsEditModalOpen(true);
  };

  const handleUpdateProgress = async () => {
    if (!selectedParticipant) return;

    try {
      const updateData: any = {
        current_stage: editForm.current_stage,
        stage_status: editForm.stage_status,
        notes: editForm.notes,
        updated_at: new Date().toISOString()
      };

      // Add date fields only if they have values
      if (editForm.interview_date) updateData.interview_date = editForm.interview_date;
      if (editForm.exam_payment_date) updateData.exam_payment_date = editForm.exam_payment_date;
      if (editForm.exam_scheduled_date) updateData.exam_scheduled_date = editForm.exam_scheduled_date;
      if (editForm.reregistration_payment_date) updateData.reregistration_payment_date = editForm.reregistration_payment_date;
      if (editForm.active_participant_date) updateData.active_participant_date = editForm.active_participant_date;

      const { error } = await supabase
        .from('progress_tracking')
        .upsert({
          user_id: selectedParticipant.user_id,
          ...updateData
        });

      if (error) {
        console.error('Error updating progress:', error);
        toast({
          title: "Error",
          description: "Gagal mengupdate progress peserta",
          variant: "destructive"
        });
        return;
      }

      toast({
        title: "Berhasil",
        description: "Progress peserta berhasil diupdate"
      });

      setIsEditModalOpen(false);
      refresh(); // Use cached refresh instead
    } catch (error) {
      console.error('Error:', error);
      toast({
        title: "Error",
        description: "Terjadi kesalahan saat mengupdate data",
        variant: "destructive"
      });
    }
  };

  const getStageTitle = (stage: string) => {
    const stageMap: { [key: string]: string } = {
      'registration': 'Pendaftaran',
      'interview': 'Wawancara',
      'account_created': 'Akun Dibuat',
      'exam_payment': 'Pembayaran Ujian',
      'exam_scheduled': 'Ujian Terjadwal',
      'reregistration_payment': 'Pembayaran Ulang',
      'active_participant': 'Peserta Aktif'
    };
    return stageMap[stage] || stage;
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">Selesai</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Menunggu</Badge>;
      case 'current':
        return <Badge className="bg-blue-100 text-blue-800">Berlangsung</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const stats = [
    {
      title: "Total Peserta",
      value: participants.length,
      icon: Users,
      color: "text-blue-600"
    },
    {
      title: "Peserta Aktif",
      value: participants.filter(p => p.current_stage === 'active_participant').length,
      icon: UserCheck,
      color: "text-green-600"
    },
    {
      title: "Menunggu Wawancara",
      value: participants.filter(p => p.current_stage === 'interview' && p.stage_status === 'pending').length,
      icon: Clock,
      color: "text-yellow-600"
    },
    {
      title: "Selesai Ujian",
      value: participants.filter(p => p.exam_payment_date && p.exam_scheduled_date).length,
      icon: CheckCircle,
      color: "text-purple-600"
    }
  ];

  if (loading && !participants.length) {
    return <AdminDashboardSkeleton />;
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard Admin MCU</h1>
          <p className="text-gray-600">Kelola progress dan status peserta program</p>
          <div className="mt-2 flex items-center gap-2">
            <Button
              onClick={refresh}
              variant="outline"
              size="sm"
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
              Refresh Data
            </Button>
            {isStale && (
              <Badge variant="outline" className="text-orange-600">
                Data mungkin tidak terbaru
              </Badge>
            )}
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                  <stat.icon className={`h-8 w-8 ${stat.color}`} />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Participants Table */}
        <Card>
          <CardHeader>
            <CardTitle>Daftar Peserta ({participants.length} total)</CardTitle>
          </CardHeader>
          <CardContent>
            {participants.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">Tidak ada data peserta ditemukan</p>
                <Button
                  onClick={refresh}
                  variant="outline"
                  className="mt-4"
                  disabled={loading}
                >
                  <RefreshCw className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
                  Refresh Data
                </Button>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Nama</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Tahap Saat Ini</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {participants.map((participant) => (
                    <TableRow key={participant.user_id}>
                      <TableCell className="font-medium">{participant.full_name}</TableCell>
                      <TableCell>{participant.email}</TableCell>
                      <TableCell>{getStageTitle(participant.current_stage)}</TableCell>
                      <TableCell>{getStatusBadge(participant.stage_status)}</TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditParticipant(participant)}
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* Edit Modal */}
        <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Edit Progress Peserta</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="current_stage">Tahap Saat Ini</Label>
                <Select value={editForm.current_stage} onValueChange={(value) => setEditForm({...editForm, current_stage: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="registration">Pendaftaran</SelectItem>
                    <SelectItem value="interview">Wawancara</SelectItem>
                    <SelectItem value="account_created">Akun Dibuat</SelectItem>
                    <SelectItem value="exam_payment">Pembayaran Ujian</SelectItem>
                    <SelectItem value="exam_scheduled">Ujian Terjadwal</SelectItem>
                    <SelectItem value="reregistration_payment">Pembayaran Ulang</SelectItem>
                    <SelectItem value="active_participant">Peserta Aktif</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="stage_status">Status</Label>
                <Select value={editForm.stage_status} onValueChange={(value) => setEditForm({...editForm, stage_status: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">Menunggu</SelectItem>
                    <SelectItem value="current">Berlangsung</SelectItem>
                    <SelectItem value="completed">Selesai</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="interview_date">Tanggal Wawancara</Label>
                <Input
                  id="interview_date"
                  type="date"
                  value={editForm.interview_date}
                  onChange={(e) => setEditForm({...editForm, interview_date: e.target.value})}
                />
              </div>

              <div>
                <Label htmlFor="exam_payment_date">Tanggal Pembayaran Ujian</Label>
                <Input
                  id="exam_payment_date"
                  type="date"
                  value={editForm.exam_payment_date}
                  onChange={(e) => setEditForm({...editForm, exam_payment_date: e.target.value})}
                />
              </div>

              <div>
                <Label htmlFor="exam_scheduled_date">Tanggal Ujian</Label>
                <Input
                  id="exam_scheduled_date"
                  type="date"
                  value={editForm.exam_scheduled_date}
                  onChange={(e) => setEditForm({...editForm, exam_scheduled_date: e.target.value})}
                />
              </div>

              <div>
                <Label htmlFor="notes">Catatan</Label>
                <Textarea
                  id="notes"
                  value={editForm.notes}
                  onChange={(e) => setEditForm({...editForm, notes: e.target.value})}
                  placeholder="Tambahkan catatan..."
                />
              </div>

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
                  Batal
                </Button>
                <Button onClick={handleUpdateProgress}>
                  Simpan
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default MCUAdminDashboard;
