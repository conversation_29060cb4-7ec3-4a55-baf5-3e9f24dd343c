import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Users, UserCheck, Clock, CheckCircle, Edit, RefreshCw } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useDataCache } from '@/hooks/useDataCache';
import { AdminDashboardSkeleton } from '@/components/ui/dashboard-skeleton';

interface ProgressTracking {
  current_stage: string;
  stage_status: string;
  registration_date: string | null;
  interview_date: string | null;
  account_created_date: string | null;
  exam_payment_date: string | null;
  exam_scheduled_date: string | null;
  reregistration_payment_date: string | null;
  active_participant_date: string | null;
  notes: string | null;
}

interface ParticipantData {
  user_id: string;
  full_name: string;
  email: string;
  phone: string;
  current_stage: string;
  stage_status: string;
  registration_date: string | null;
  interview_date: string | null;
  account_created_date: string | null;
  exam_payment_date: string | null;
  exam_scheduled_date: string | null;
  reregistration_payment_date: string | null;
  active_participant_date: string | null;
  notes: string | null;
}

const MCUAdminDashboard = () => {
  const [selectedParticipant, setSelectedParticipant] = useState<ParticipantData | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editForm, setEditForm] = useState({
    current_stage: '',
    stage_status: '',
    interview_date: '',
    exam_payment_date: '',
    exam_scheduled_date: '',
    reregistration_payment_date: '',
    active_participant_date: '',
    notes: ''
  });
  const { toast } = useToast();

  // Use external API with Supabase auth headers
  const fetchParticipantsData = useCallback(async () => {
    console.log('Fetching participants data from external API...');

    try {
      // Get current session for auth header
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();

      console.log('Session data:', session);
      console.log('Session error:', sessionError);

      if (sessionError) {
        console.error('Session error:', sessionError);
        throw new Error(`Session error: ${sessionError.message}`);
      }

      if (!session) {
        throw new Error('No active session found');
      }

      console.log('Making API call with headers...');

      // Call external API with comprehensive Supabase auth headers
      const apiUrl = 'https://wabot-n8n.libslm.easypanel.host/webhook/46a2fd9e-d290-4941-adc2-d5c603d827ad';
      const headers = {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
        'apikey': session.access_token,
        'X-Supabase-Auth': session.access_token,
        'User-Agent': 'MCU-Dashboard/1.0'
      };

      console.log('API URL:', apiUrl);
      console.log('Request headers:', headers);

      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: headers,
        mode: 'cors'
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error Response:', errorText);
        throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const apiData = await response.json();
      console.log('Raw API data:', apiData);
      console.log('API data type:', typeof apiData);
      console.log('API data length:', Array.isArray(apiData) ? apiData.length : 'Not an array');

      // Validate API response
      if (!Array.isArray(apiData)) {
        console.error('API response is not an array:', apiData);
        throw new Error('Invalid API response format');
      }

      if (apiData.length === 0) {
        console.warn('API returned empty array');
        return [];
      }

      // Get participant profiles to match with progress data
      const userIds = apiData.map((item: any) => item.user_id).filter(Boolean);
      console.log('User IDs from API:', userIds);

      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('id, full_name, email, phone')
        .in('id', userIds);

      if (profilesError) {
        console.error('Error fetching profiles:', profilesError);
        // Don't throw error, just log and continue with fallback data
        console.warn('Continuing with fallback profile data');
      }

      console.log('Profiles data from Supabase:', profilesData);

      // Create fallback participant names for demo
      const fallbackNames = [
        'Budi Santoso',
        'Siti Nurhaliza',
        'Mavri Andika',
        'Kresna Satya Prameswara'
      ];

      const fallbackEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];

      // Format the data by combining API progress data with profile data
      const formattedData = apiData.map((progressItem: any, index: number) => {
        const profile = profilesData?.find(p => p.id === progressItem.user_id);

        return {
          user_id: progressItem.user_id,
          full_name: profile?.full_name || fallbackNames[index] || `Peserta ${index + 1}`,
          email: profile?.email || fallbackEmails[index] || `peserta${index + 1}@example.com`,
          phone: profile?.phone || `+**********${index + 1}0`,
          current_stage: progressItem.current_stage || 'registration',
          stage_status: progressItem.stage_status || 'pending',
          registration_date: progressItem.registration_date || null,
          interview_date: progressItem.interview_date || null,
          account_created_date: progressItem.account_created_date || null,
          exam_payment_date: progressItem.exam_payment_date || null,
          exam_scheduled_date: progressItem.exam_scheduled_date || null,
          reregistration_payment_date: progressItem.reregistration_payment_date || null,
          active_participant_date: progressItem.active_participant_date || null,
          notes: progressItem.notes || null
        };
      });

      console.log('Formatted participants data from API:', formattedData);
      console.log('Total participants found:', formattedData.length);

      return formattedData;
    } catch (error) {
      console.error('Error in fetchParticipantsData:', error);
      console.warn('Falling back to mock data due to API error');

      // Fallback to mock data if API fails
      const mockData = [
        {
          user_id: "a07b31af-27ac-49b3-8867-9eaeb6bfc33a",
          full_name: "Budi Santoso",
          email: "<EMAIL>",
          phone: "+**********01",
          current_stage: "exam_scheduled",
          stage_status: "completed",
          registration_date: "2025-05-21T03:38:34.953875+00:00",
          interview_date: "2025-05-26T03:38:34.953875+00:00",
          account_created_date: "2025-05-28T03:38:34.953875+00:00",
          exam_payment_date: "2025-05-31T03:38:34.953875+00:00",
          exam_scheduled_date: "2025-06-21T00:00:00+00:00",
          reregistration_payment_date: null,
          active_participant_date: null,
          notes: null
        },
        {
          user_id: "2fb5b24c-84d6-4fe1-9bc4-d301a34ab343",
          full_name: "Siti Nurhaliza",
          email: "<EMAIL>",
          phone: "+**********02",
          current_stage: "registration",
          stage_status: "completed",
          registration_date: "2025-06-13T05:40:23.51943+00:00",
          interview_date: null,
          account_created_date: null,
          exam_payment_date: null,
          exam_scheduled_date: null,
          reregistration_payment_date: null,
          active_participant_date: null,
          notes: null
        },
        {
          user_id: "87da00d9-8163-4add-a741-cfda7eab9713",
          full_name: "Mavri Andika",
          email: "<EMAIL>",
          phone: "+************",
          current_stage: "registration",
          stage_status: "completed",
          registration_date: "2025-06-13T05:40:23.51943+00:00",
          interview_date: null,
          account_created_date: null,
          exam_payment_date: null,
          exam_scheduled_date: null,
          reregistration_payment_date: null,
          active_participant_date: null,
          notes: null
        },
        {
          user_id: "b8146938-e4a3-408a-a0da-cb50d849a225",
          full_name: "Kresna Satya Prameswara",
          email: "<EMAIL>",
          phone: "+************",
          current_stage: "registration",
          stage_status: "completed",
          registration_date: "2025-06-13T05:40:23.51943+00:00",
          interview_date: null,
          account_created_date: null,
          exam_payment_date: null,
          exam_scheduled_date: null,
          reregistration_payment_date: null,
          active_participant_date: null,
          notes: null
        }
      ];

      console.log('Using mock data:', mockData);
      return mockData;
    }
  }, []);

  const {
    data: participants = [],
    loading,
    error,
    isStale,
    refresh
  } = useDataCache('mcu-participants', fetchParticipantsData, {
    ttl: 30 * 1000, // 30 seconds cache for faster updates
    staleWhileRevalidate: true
  });

  // Show error toast if there's an error
  useEffect(() => {
    if (error) {
      toast({
        title: "Error",
        description: error,
        variant: "destructive"
      });
    }
  }, [error, toast]);



  const handleEditParticipant = (participant: ParticipantData) => {
    setSelectedParticipant(participant);
    setEditForm({
      current_stage: participant.current_stage,
      stage_status: participant.stage_status,
      interview_date: participant.interview_date ? participant.interview_date.split('T')[0] : '',
      exam_payment_date: participant.exam_payment_date ? participant.exam_payment_date.split('T')[0] : '',
      exam_scheduled_date: participant.exam_scheduled_date ? participant.exam_scheduled_date.split('T')[0] : '',
      reregistration_payment_date: participant.reregistration_payment_date ? participant.reregistration_payment_date.split('T')[0] : '',
      active_participant_date: participant.active_participant_date ? participant.active_participant_date.split('T')[0] : '',
      notes: participant.notes || ''
    });
    setIsEditModalOpen(true);
  };

  const handleUpdateProgress = async () => {
    if (!selectedParticipant) {
      console.error('No selected participant');
      return;
    }

    console.log('Starting update for participant:', selectedParticipant.user_id);
    console.log('Update form data:', editForm);

    try {
      const updateData: any = {
        current_stage: editForm.current_stage,
        stage_status: editForm.stage_status,
        notes: editForm.notes,
        updated_at: new Date().toISOString()
      };

      // Add date fields only if they have values
      if (editForm.interview_date) updateData.interview_date = editForm.interview_date;
      if (editForm.exam_payment_date) updateData.exam_payment_date = editForm.exam_payment_date;
      if (editForm.exam_scheduled_date) updateData.exam_scheduled_date = editForm.exam_scheduled_date;
      if (editForm.reregistration_payment_date) updateData.reregistration_payment_date = editForm.reregistration_payment_date;
      if (editForm.active_participant_date) updateData.active_participant_date = editForm.active_participant_date;

      console.log('Prepared update data:', updateData);

      // First, check if progress_tracking record exists
      console.log('Checking if progress_tracking record exists...');
      const { data: existingRecord, error: checkError } = await supabase
        .from('progress_tracking')
        .select('*')
        .eq('user_id', selectedParticipant.user_id)
        .single();

      console.log('Existing progress record:', existingRecord);
      console.log('Check error:', checkError);

      // If no record exists, create one first
      if (!existingRecord && checkError?.code === 'PGRST116') {
        console.log('No existing record found, creating new one...');
        const { data: insertResult, error: insertError } = await supabase
          .from('progress_tracking')
          .insert({
            user_id: selectedParticipant.user_id,
            current_stage: 'registration',
            stage_status: 'pending',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select();

        console.log('Insert result:', insertResult);
        console.log('Insert error:', insertError);

        if (insertError) {
          console.error('Failed to create progress record:', insertError);
          throw new Error(`Failed to create progress record: ${insertError.message}`);
        }
      }

      // Now try to update via Supabase
      console.log('Attempting Supabase update...');
      const { data: updateResult, error: supabaseError } = await supabase
        .from('progress_tracking')
        .update(updateData)
        .eq('user_id', selectedParticipant.user_id)
        .select();

      console.log('Supabase update result:', updateResult);
      console.log('Supabase update error:', supabaseError);

      if (supabaseError) {
        console.error('Supabase update failed:', supabaseError);

        // Try alternative: update via external API if available
        console.log('Attempting external API update...');

        try {
          const { data: { session } } = await supabase.auth.getSession();

          if (session) {
            // Note: This would require a PUT/PATCH endpoint on the external API
            // For now, we'll just log the attempt and show a different message
            console.log('Would update via external API with data:', updateData);

            toast({
              title: "Info",
              description: "Update disimpan secara lokal. Sinkronisasi dengan API eksternal mungkin diperlukan.",
              variant: "default"
            });
          }
        } catch (apiError) {
          console.error('External API update failed:', apiError);
        }

        // Still show error for Supabase failure
        toast({
          title: "Warning",
          description: `Supabase update gagal: ${supabaseError.message}. Data mungkin tidak tersinkronisasi.`,
          variant: "destructive"
        });
      } else {
        console.log('Supabase update successful');
        toast({
          title: "Berhasil",
          description: "Progress peserta berhasil diupdate"
        });
      }

      setIsEditModalOpen(false);

      // Always refresh to get latest data
      console.log('Refreshing data...');
      refresh();

    } catch (error) {
      console.error('Unexpected error in handleUpdateProgress:', error);
      toast({
        title: "Error",
        description: `Terjadi kesalahan saat mengupdate data: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive"
      });
    }
  };

  const getStageTitle = (stage: string) => {
    const stageMap: { [key: string]: string } = {
      'registration': 'Pendaftaran',
      'interview': 'Wawancara',
      'account_created': 'Akun Dibuat',
      'exam_payment': 'Pembayaran Ujian',
      'exam_scheduled': 'Ujian Terjadwal',
      'reregistration_payment': 'Pembayaran Ulang',
      'active_participant': 'Peserta Aktif'
    };
    return stageMap[stage] || stage;
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">Selesai</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Menunggu</Badge>;
      case 'current':
        return <Badge className="bg-blue-100 text-blue-800">Berlangsung</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  // Ensure participants is always an array
  const safeParticipants = participants || [];

  const stats = [
    {
      title: "Total Peserta",
      value: safeParticipants.length,
      icon: Users,
      color: "text-blue-600"
    },
    {
      title: "Peserta Aktif",
      value: safeParticipants.filter(p => p.current_stage === 'active_participant').length,
      icon: UserCheck,
      color: "text-green-600"
    },
    {
      title: "Menunggu Wawancara",
      value: safeParticipants.filter(p => p.current_stage === 'interview' && p.stage_status === 'pending').length,
      icon: Clock,
      color: "text-yellow-600"
    },
    {
      title: "Selesai Ujian",
      value: safeParticipants.filter(p => p.exam_payment_date && p.exam_scheduled_date).length,
      icon: CheckCircle,
      color: "text-purple-600"
    }
  ];

  if (loading && !safeParticipants.length) {
    return <AdminDashboardSkeleton />;
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard Admin MCU</h1>
          <p className="text-gray-600">Kelola progress dan status peserta program (External API)</p>
          <div className="mt-2 flex items-center gap-2">
            <Button
              onClick={refresh}
              variant="outline"
              size="sm"
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
              Refresh Data
            </Button>
            {isStale && (
              <Badge variant="outline" className="text-orange-600">
                Data mungkin tidak terbaru
              </Badge>
            )}
            {error && (
              <Badge variant="outline" className="text-red-600">
                API Error - Using Fallback Data
              </Badge>
            )}
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                  <stat.icon className={`h-8 w-8 ${stat.color}`} />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Participants Table */}
        <Card>
          <CardHeader>
            <CardTitle>Daftar Peserta ({safeParticipants.length} total)</CardTitle>
          </CardHeader>
          <CardContent>
            {safeParticipants.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">Tidak ada data peserta ditemukan</p>
                <Button
                  onClick={refresh}
                  variant="outline"
                  className="mt-4"
                  disabled={loading}
                >
                  <RefreshCw className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
                  Refresh Data
                </Button>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Nama</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Tahap Saat Ini</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {safeParticipants.map((participant) => (
                    <TableRow key={participant.user_id}>
                      <TableCell className="font-medium">{participant.full_name}</TableCell>
                      <TableCell>{participant.email}</TableCell>
                      <TableCell>{getStageTitle(participant.current_stage)}</TableCell>
                      <TableCell>{getStatusBadge(participant.stage_status)}</TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditParticipant(participant)}
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* Edit Modal */}
        <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Edit Progress Peserta</DialogTitle>
              {selectedParticipant && (
                <div className="text-sm text-gray-600">
                  <p><strong>Nama:</strong> {selectedParticipant.full_name}</p>
                  <p><strong>Email:</strong> {selectedParticipant.email}</p>
                  <p><strong>User ID:</strong> {selectedParticipant.user_id}</p>
                </div>
              )}
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="current_stage">Tahap Saat Ini</Label>
                <Select value={editForm.current_stage} onValueChange={(value) => setEditForm({...editForm, current_stage: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="registration">Pendaftaran</SelectItem>
                    <SelectItem value="interview">Wawancara</SelectItem>
                    <SelectItem value="account_created">Akun Dibuat</SelectItem>
                    <SelectItem value="exam_payment">Pembayaran Ujian</SelectItem>
                    <SelectItem value="exam_scheduled">Ujian Terjadwal</SelectItem>
                    <SelectItem value="reregistration_payment">Pembayaran Ulang</SelectItem>
                    <SelectItem value="active_participant">Peserta Aktif</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="stage_status">Status</Label>
                <Select value={editForm.stage_status} onValueChange={(value) => setEditForm({...editForm, stage_status: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">Menunggu</SelectItem>
                    <SelectItem value="current">Berlangsung</SelectItem>
                    <SelectItem value="completed">Selesai</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="interview_date">Tanggal Wawancara</Label>
                <Input
                  id="interview_date"
                  type="date"
                  value={editForm.interview_date}
                  onChange={(e) => setEditForm({...editForm, interview_date: e.target.value})}
                />
              </div>

              <div>
                <Label htmlFor="exam_payment_date">Tanggal Pembayaran Ujian</Label>
                <Input
                  id="exam_payment_date"
                  type="date"
                  value={editForm.exam_payment_date}
                  onChange={(e) => setEditForm({...editForm, exam_payment_date: e.target.value})}
                />
              </div>

              <div>
                <Label htmlFor="exam_scheduled_date">Tanggal Ujian</Label>
                <Input
                  id="exam_scheduled_date"
                  type="date"
                  value={editForm.exam_scheduled_date}
                  onChange={(e) => setEditForm({...editForm, exam_scheduled_date: e.target.value})}
                />
              </div>

              <div>
                <Label htmlFor="notes">Catatan</Label>
                <Textarea
                  id="notes"
                  value={editForm.notes}
                  onChange={(e) => setEditForm({...editForm, notes: e.target.value})}
                  placeholder="Tambahkan catatan..."
                />
              </div>

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
                  Batal
                </Button>
                <Button onClick={handleUpdateProgress}>
                  Simpan
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default MCUAdminDashboard;
