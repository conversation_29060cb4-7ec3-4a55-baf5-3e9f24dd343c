
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/useAuth';
import { LogOut, User } from 'lucide-react';
import AuthModal from '@/components/AuthModal';

const Navbar = () => {
  const { user, logout, isAuthenticated } = useAuth();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authModalTab, setAuthModalTab] = useState<'login' | 'signup'>('login');

  const handleLoginClick = () => {
    setAuthModalTab('login');
    setShowAuthModal(true);
  };

  const handleRegisterClick = () => {
    setAuthModalTab('signup');
    setShowAuthModal(true);
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'participant':
        return 'Peserta';
      case 'mcu_admin':
        return 'Admin MCU';
      case 'ui_admin':
        return 'Admin UI';
      default:
        return role;
    }
  };

  return (
    <>
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <img 
                src="/placeholder.svg" 
                alt="CSEL UI Logo" 
                className="h-8 w-auto"
              />
              <span className="ml-3 text-xl font-bold text-blue-900">CSEL UI</span>
            </div>

            <div className="flex items-center space-x-4">
              {isAuthenticated ? (
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    <User className="h-4 w-4 text-gray-600" />
                    <span className="text-sm text-gray-700">{user?.email}</span>
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                      {getRoleLabel(user?.role || '')}
                    </span>
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={logout}
                    className="flex items-center space-x-1"
                  >
                    <LogOut className="h-4 w-4" />
                    <span>Logout</span>
                  </Button>
                </div>
              ) : (
                <div className="space-x-2">
                  <Button variant="outline" size="sm" onClick={handleLoginClick}>
                    Login
                  </Button>
                  <Button size="sm" className="bg-blue-600 hover:bg-blue-700" onClick={handleRegisterClick}>
                    Register
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </nav>

      <AuthModal 
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        defaultTab={authModalTab}
      />
    </>
  );
};

export default Navbar;
