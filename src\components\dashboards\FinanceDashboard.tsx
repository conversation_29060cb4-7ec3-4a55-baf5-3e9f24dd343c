
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON>hart, Pie, Cell } from 'recharts';
import { DollarSign, TrendingUp, Download, RefreshCw, Calendar } from 'lucide-react';

const FinanceDashboard = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('monthly');

  // Mock financial data
  const monthlyRevenue = [
    { month: 'Jan', DBE: 4500000, MiniMBA: 3600000 },
    { month: 'Feb', DBE: 6000000, MiniMBA: 4200000 },
    { month: 'Mar', DBE: 5400000, MiniMBA: 3900000 },
    { month: 'Apr', DBE: 7200000, MiniMBA: 5100000 },
    { month: 'May', DBE: 6300000, MiniMBA: 4800000 },
    { month: 'Jun', DBE: 8100000, MiniMBA: 5700000 }
  ];

  const programDistribution = [
    { name: 'DBE', value: 37500000, color: '#3b82f6' },
    { name: 'Mini MBA', value: 27300000, color: '#06b6d4' }
  ];

  const stats = [
    {
      title: 'Total Revenue (This Month)',
      value: 'Rp 13,800,000',
      change: '+12.5%',
      icon: DollarSign,
      color: 'text-green-600'
    },
    {
      title: 'DBE Program Revenue',
      value: 'Rp 8,100,000',
      change: '+8.2%',
      icon: TrendingUp,
      color: 'text-blue-600'
    },
    {
      title: 'Mini MBA Revenue',
      value: 'Rp 5,700,000',
      change: '+18.7%',
      icon: TrendingUp,
      color: 'text-cyan-600'
    },
    {
      title: 'Pending Payments',
      value: 'Rp 2,400,000',
      change: '-5.1%',
      icon: Calendar,
      color: 'text-yellow-600'
    }
  ];

  const transactions = [
    {
      id: 1,
      participant: 'John Doe',
      program: 'DBE',
      amount: 300000,
      type: 'Exam Fee',
      date: '2024-06-15',
      status: 'Verified'
    },
    {
      id: 2,
      participant: 'Jane Smith',
      program: 'Mini MBA',
      amount: 300000,
      type: 'Re-registration',
      date: '2024-06-14',
      status: 'Verified'
    },
    {
      id: 3,
      participant: 'Mike Johnson',
      program: 'DBE',
      amount: 300000,
      type: 'Exam Fee',
      date: '2024-06-13',
      status: 'Pending'
    }
  ];

  const withdrawalRecords = [
    {
      id: 1,
      month: 'May 2024',
      totalAmount: 11100000,
      withdrawnAmount: 10000000,
      withdrawalDate: '2024-06-01',
      status: 'Completed'
    },
    {
      id: 2,
      month: 'April 2024',
      totalAmount: 12300000,
      withdrawnAmount: 12000000,
      withdrawalDate: '2024-05-01',
      status: 'Completed'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Finance Dashboard</h1>
          <p className="text-gray-600">Track revenue, manage withdrawals, and generate financial reports</p>
        </div>

        {/* Stats Grid */}
        <div className="grid md:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className={`text-sm ${stat.color}`}>{stat.change} from last month</p>
                  </div>
                  <stat.icon className={`h-8 w-8 ${stat.color}`} />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Revenue Overview</TabsTrigger>
            <TabsTrigger value="transactions">Transactions</TabsTrigger>
            <TabsTrigger value="withdrawals">Withdrawals</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid lg:grid-cols-3 gap-6">
              {/* Revenue Chart */}
              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle>Monthly Revenue by Program</CardTitle>
                  <CardDescription>Track revenue performance across DBE and Mini MBA programs</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={monthlyRevenue}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis tickFormatter={(value) => `${value / 1000000}M`} />
                      <Tooltip formatter={(value) => [`Rp ${Number(value).toLocaleString('id-ID')}`, '']} />
                      <Bar dataKey="DBE" fill="#3b82f6" name="DBE" />
                      <Bar dataKey="MiniMBA" fill="#06b6d4" name="Mini MBA" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Program Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle>Program Revenue Distribution</CardTitle>
                  <CardDescription>Revenue breakdown by program type</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={programDistribution}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {programDistribution.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`Rp ${Number(value).toLocaleString('id-ID')}`, '']} />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="transactions">
            <Card>
              <CardHeader>
                <CardTitle>Recent Transactions</CardTitle>
                <CardDescription>All payment transactions and their verification status</CardDescription>
                <div className="flex items-center space-x-2">
                  <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {transactions.map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <p className="font-medium">{transaction.participant}</p>
                        <p className="text-sm text-gray-600">
                          {transaction.program} • {transaction.type}
                        </p>
                        <p className="text-xs text-gray-500">{transaction.date}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">Rp {transaction.amount.toLocaleString('id-ID')}</p>
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          transaction.status === 'Verified' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {transaction.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="withdrawals">
            <Card>
              <CardHeader>
                <CardTitle>Withdrawal Management</CardTitle>
                <CardDescription>Manage monthly cash withdrawals and track balances</CardDescription>
                <Button className="w-fit bg-blue-600 hover:bg-blue-700">
                  <Download className="h-4 w-4 mr-2" />
                  Process Monthly Withdrawal
                </Button>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {withdrawalRecords.map((record) => (
                    <div key={record.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <p className="font-medium">{record.month}</p>
                        <p className="text-sm text-gray-600">
                          Total: Rp {record.totalAmount.toLocaleString('id-ID')} • 
                          Withdrawn: Rp {record.withdrawnAmount.toLocaleString('id-ID')}
                        </p>
                        <p className="text-xs text-gray-500">Processed: {record.withdrawalDate}</p>
                      </div>
                      <div className="text-right">
                        <span className="text-xs px-2 py-1 rounded-full bg-green-100 text-green-800">
                          {record.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="reports">
            <Card>
              <CardHeader>
                <CardTitle>Financial Reports</CardTitle>
                <CardDescription>Generate comprehensive reports for UI management</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Monthly Reports</h3>
                    <div className="space-y-2">
                      <Button variant="outline" className="w-full justify-start">
                        <Download className="h-4 w-4 mr-2" />
                        Revenue Summary Report
                      </Button>
                      <Button variant="outline" className="w-full justify-start">
                        <Download className="h-4 w-4 mr-2" />
                        Transaction Details Report
                      </Button>
                      <Button variant="outline" className="w-full justify-start">
                        <Download className="h-4 w-4 mr-2" />
                        Program Performance Report
                      </Button>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Quarterly Reports</h3>
                    <div className="space-y-2">
                      <Button variant="outline" className="w-full justify-start">
                        <Download className="h-4 w-4 mr-2" />
                        Q2 2024 Financial Summary
                      </Button>
                      <Button variant="outline" className="w-full justify-start">
                        <Download className="h-4 w-4 mr-2" />
                        UKK CSEL UI Performance Report
                      </Button>
                      <Button variant="outline" className="w-full justify-start">
                        <Download className="h-4 w-4 mr-2" />
                        Audit Trail Report
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default FinanceDashboard;
