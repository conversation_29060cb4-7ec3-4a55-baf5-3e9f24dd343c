
import React from 'react';
import { useAuth } from '@/hooks/useAuth';
import Navbar from '@/components/Navbar';
import LandingPage from '@/components/LandingPage';
import ParticipantDashboard from '@/components/dashboards/ParticipantDashboard';
import MCUAdminDashboard from '@/components/dashboards/MCUAdminDashboard';
import UIAdminDashboard from '@/components/dashboards/UIAdminDashboard';
import { Loader2, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';

const Index = () => {
  const { user, isAuthenticated, loading, error } = useAuth();

  // Show error state if there's an authentication error
  if (error && !loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2"><PERSON><PERSON><PERSON><PERSON></h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button 
            onClick={() => window.location.reload()} 
            className="bg-blue-600 hover:bg-blue-700"
          >
            Muat Ulang Halaman
          </Button>
        </div>
      </div>
    );
  }

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Memuat...</p>
          <p className="text-sm text-gray-500 mt-2">Sedang memverifikasi autentikasi</p>
        </div>
      </div>
    );
  }

  const renderDashboard = () => {
    if (!isAuthenticated) {
      return <LandingPage />;
    }

    console.log('Rendering dashboard for role:', user?.role);
    
    switch (user?.role) {
      case 'participant':
        return <ParticipantDashboard />;
      case 'mcu_admin':
        return <MCUAdminDashboard />;
      case 'ui_admin':
        return <UIAdminDashboard />;
      default:
        console.log('Unknown role, showing landing page');
        return <LandingPage />;
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      {renderDashboard()}
    </div>
  );
};

export default Index;
