-- Create trigger function to handle new program registration
CREATE OR REPLACE FUNCTION public.handle_new_program_registration()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = ''
AS $$
BEGIN
  -- Create progress tracking record for new program registration
  INSERT INTO public.progress_tracking (
    user_id, 
    current_stage, 
    stage_status, 
    registration_date
  )
  VALUES (
    NEW.user_id,
    'registration',
    'completed',
    NEW.created_at
  )
  ON CONFLICT (user_id) DO UPDATE SET
    registration_date = COALESCE(public.progress_tracking.registration_date, NEW.created_at),
    updated_at = NOW();
  
  RETURN NEW;
END;
$$;

-- Create trigger to automatically create progress tracking on program registration
CREATE TRIGGER on_program_registration_created
  AFTER INSERT ON public.program_registrations
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_program_registration();

-- Create progress tracking for existing program registrations that don't have it
INSERT INTO public.progress_tracking (user_id, current_stage, stage_status, registration_date)
SELECT 
  pr.user_id,
  'registration',
  'completed',
  pr.created_at
FROM public.program_registrations pr
LEFT JOIN public.progress_tracking pt ON pr.user_id = pt.user_id
WHERE pt.user_id IS NULL;
