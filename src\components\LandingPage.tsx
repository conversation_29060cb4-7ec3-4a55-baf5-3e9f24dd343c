import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Check, ArrowRight, BookOpen, Users, Award, TrendingUp, Star, FileText, MessageCircle, CreditCard, Play } from 'lucide-react';
import RegistrationModal from './RegistrationModal';
import AuthModal from './AuthModal';

const LandingPage = () => {
  const [showRegistration, setShowRegistration] = useState(false);
  const [showAuth, setShowAuth] = useState(false);

  const programs = [
    {
      name: "Exclusive Class Real Estate Program",
      type: "",
      duration: "3 bulan",
      price: "Hubungi panitia",
      image: "/lovable-uploads/2fe23bdc-9f2f-4ce4-882f-58bd380dfc51.png",
      rating: 4.5,
      participants: 20,
      features: [
        "Program pendidikan non-degree atau sertifikas",
        "Konsep short course selama 3 bulan",
        "Pengalaman belajar dengan memaksimalkan potensi",
        "Teori program yang sangat inovatif"
      ]
    },
    {
      name: "Digital Business & Entrepreneurship Program",
      type: "",
      duration: "1 tahun", 
      price: "Hubungi panitia",
      image: "/lovable-uploads/eb8a70ce-cb55-456a-87bc-dd1e7a054cc7.png",
      rating: 4.8,
      participants: 45,
      features: [
        "Program pendidikan non-degree atau sertifikas",
        "Konsep short course",
        "Digital business dan kewirausahaan",
        "Pengembangan keahlian dalam karir"
      ]
    },
    {
      name: "Property Investment Masterclass",
      type: "",
      duration: "2 hari",
      price: "Hubungi panitia",
      image: "/lovable-uploads/84ebb5b3-9cb9-43cb-b89f-ab1f0a86f974.png",
      rating: 4.7,
      participants: 30,
      features: [
        "Strategi master investasi properti",
        "Kerjasama dengan MCU",
        "Sertifikat dari UI dan Grade Perusahaan",
        "Developer dan Investor Properti"
      ]
    }
  ];

  const stats = [
    { icon: Users, label: "Leadership & Entrepreneur", value: "2,500+" },
    { icon: Award, label: "Data Science for Business", value: "95%" },
    { icon: BookOpen, label: "Short Course Skill Oriented Program", value: "3" },
    { icon: TrendingUp, label: "Entrepreneurialship Certification", value: "88%" }
  ];

  const benefits = [
    {
      icon: "🎓",
      title: "Jaminan Bekerja Setelah Lulus",
      description: "Menjamin kesempatan kerja bagi lulusan, memastikan transisi mulus ke dunia profesional."
    },
    {
      icon: "🏢", 
      title: "Terkoneksi dengan Perusahaan Industri",
      description: "Akses langsung ke jaringan perusahaan terkemuka, membuka peluang kolaborasi dan pekerjaan."
    },
    {
      icon: "👥",
      title: "Pengajar dari Praktisi dan Akademi Berkualitas", 
      description: "Pelajaran dari para praktisi dan akademisi berpengalaman, menjamin pendidikan berkualitas tinggi."
    },
    {
      icon: "💼",
      title: "Pendampingan Karir dan Bisnis Sejak Awal Kuliah",
      description: "Bimbingan karir dan bisnis terintegrasi serta mendukung perkembangan profesional mahasiswa."
    },
    {
      icon: "🏆",
      title: "Sertifikat & Lisensi Profesional",
      description: "Pemberian sertifikat dan lisensi yang mendukung kemajuan karir di bidang terkait."
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-50 via-white to-yellow-50">
      {/* Header Navigation */}
      <header className="absolute top-0 left-0 right-0 z-50 bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex-shrink-0">
              <img 
                src="/lovable-uploads/1d1c02f5-65c7-40d0-9aaa-f3b9fb95d795.png" 
                alt="CSEL UI Logo" 
                className="h-12 w-auto"
              />
            </div>
            
            {/* Login and Register buttons */}
            <div className="flex space-x-4">
              <Button 
                variant="outline" 
                onClick={() => setShowAuth(true)}
                className="border-yellow-500 text-yellow-600 hover:bg-yellow-50"
              >
                Masuk
              </Button>
              <Button 
                onClick={() => setShowAuth(true)}
                className="bg-yellow-500 hover:bg-yellow-600 text-white"
              >
                Daftar
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section - Removed background image */}
      <section className="pt-20 pb-16 bg-gradient-to-r from-yellow-400 to-yellow-500 relative overflow-hidden min-h-[600px]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 h-full flex items-center">
          <div className="text-center text-white max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 mt-16">
              Center for Strategic
              <br />
              <span className="text-yellow-100">Entrepreneurial Leadership</span>
            </h1>
            <p className="text-xl text-yellow-100 mb-8 max-w-2xl mx-auto">
              Tingkatkan karir Anda dengan program pendidikan eksekutif terdepan Universitas Indonesia. 
              Transformasikan keterampilan kepemimpinan dan dorong keunggulan bisnis.
            </p>
            <div className="space-x-4">
              <Button 
                size="lg" 
                className="bg-white text-yellow-600 hover:bg-yellow-50 text-lg px-8 py-3 font-semibold"
                onClick={() => setShowRegistration(true)}
              >
                Mulai Perjalanan Anda
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button 
                variant="outline" 
                size="lg" 
                className="text-lg px-8 py-3 border-2 border-white text-white hover:bg-white hover:text-yellow-600 bg-transparent"
                onClick={() => setShowAuth(true)}
              >
                Masuk ke Dashboard
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Director Introduction */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="mb-8">
            <img 
              src="/lovable-uploads/6be38569-d0ca-4dcb-b56f-05c182a4d6c4.png" 
              alt="Dr. Roy Darmawan" 
              className="w-32 h-32 rounded-full mx-auto mb-6 shadow-lg"
            />
          </div>
          <blockquote className="text-lg text-gray-700 italic mb-6 leading-relaxed">
            "CSEL UI bertujuan menjadi pusat pengembangan penelitian, potensi sumber daya manusia (SDM), 
            serta pengembangan pendidikan kewirausahaan dan kepemimpinan berwawasan global, dengan 
            mengintegrasikan entrepreneurial mindset dan management skill sebagai modal untuk mengubah 
            tantangan menjadi peluang."
          </blockquote>
          <div className="text-center">
            <div className="font-semibold text-gray-900 text-lg">Dr. Roy Darmawan, M.Si.</div>
            <div className="text-yellow-600 font-medium">Ketua Center for Strategic Entrepreneurial Leadership</div>
            <div className="text-gray-600 text-sm">CSGS - SKSG UI</div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-yellow-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <stat.icon className="h-12 w-12 text-yellow-600 mx-auto mb-4" />
                <div className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</div>
                <div className="text-gray-600 text-sm font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Programs Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Program Kami</h2>
            <p className="text-xl text-gray-600">Pilih program yang sesuai dengan tujuan karir Anda</p>
          </div>
          
          <div className="grid lg:grid-cols-3 gap-8">
            {programs.map((program, index) => (
              <Card key={index} className="border-2 hover:border-yellow-300 transition-colors overflow-hidden">
                <div className="relative">
                  <img 
                    src={program.image} 
                    alt={program.name}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute top-4 right-4 bg-white rounded-full px-3 py-1 flex items-center space-x-1">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span className="text-sm font-medium">{program.rating}</span>
                    <span className="text-xs text-gray-600">({program.participants})</span>
                  </div>
                </div>
                <CardHeader>
                  <CardTitle className="text-xl text-yellow-900">{program.name}</CardTitle>
                  {program.type && (
                    <CardDescription className="text-lg font-medium text-yellow-700">
                      {program.type}
                    </CardDescription>
                  )}
                  <CardDescription className="text-lg">
                    {program.duration} • {program.price}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center mb-4 text-sm text-gray-600">
                    <Users className="h-4 w-4 mr-1" />
                    <span>{program.participants} peserta</span>
                    <span className="mx-2">•</span>
                    <span>{program.duration}</span>
                  </div>
                  <ul className="space-y-3 mb-6">
                    {program.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center">
                        <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button 
                    className="w-full bg-yellow-500 hover:bg-yellow-600 text-white"
                    onClick={() => setShowRegistration(true)}
                  >
                    Daftar Sekarang
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
          
          <div className="mt-8 text-center">
            <p className="text-lg text-gray-600 mb-2">
              <strong>Rp 300.000</strong> adalah harga pengambilan tes ujian
            </p>
            <p className="text-md text-gray-500">
              Untuk harga keseluruhan program silakan menghubungi panitia pendaftaran peserta short-course
            </p>
          </div>
        </div>
      </section>

      {/* Why Choose CSEL Section */}
      <section className="py-16 bg-gradient-to-r from-yellow-400 to-yellow-500">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Mengapa Memilih Belajar di CSEL UI?</h2>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-1 flex items-center justify-center">
              <div className="text-center text-white">
                <img 
                  src="/lovable-uploads/e31ef31d-7f07-432d-98c0-6a4a69b03019.png" 
                  alt="Mahasiswa CSEL" 
                  className="w-64 h-auto mx-auto mb-6 rounded-lg"
                />
              </div>
            </div>
            
            <div className="lg:col-span-2 space-y-6">
              {benefits.map((benefit, index) => (
                <div key={index} className="bg-white bg-opacity-10 backdrop-blur rounded-lg p-6 text-white">
                  <div className="flex items-start space-x-4">
                    <div className="text-3xl">{benefit.icon}</div>
                    <div>
                      <h3 className="text-lg font-semibold mb-2">{benefit.title}</h3>
                      <p className="text-yellow-100 leading-relaxed">{benefit.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Process Section - Remove background image */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Proses Pendaftaran</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Ikuti langkah mudah untuk memulai perjalanan pendidikan eksekutif Anda di CSEL UI
            </p>
          </div>
          
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Image Section */}
            <div className="order-2 lg:order-1">
              <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-2xl p-8 shadow-lg">
                <div className="text-center">
                  <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center mx-auto mb-6">
                    <Play className="h-10 w-10 text-yellow-600" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-4">Siap Memulai?</h3>
                  <p className="text-yellow-100 text-lg leading-relaxed mb-6">
                    Bergabunglah dengan ribuan profesional yang telah memajukan karir mereka bersama CSEL UI
                  </p>
                  <div className="bg-white bg-opacity-20 rounded-lg p-4">
                    <p className="text-white font-medium">
                      📞 Hubungi Tim Kami: 
                      <br />
                      <span className="text-lg font-bold">+62 21 1234 5678</span>
                    </p>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Steps Section */}
            <div className="order-1 lg:order-2 space-y-8">
              {[
                { 
                  step: "01", 
                  title: "Daftar Online", 
                  desc: "Lengkapi formulir pendaftaran online dengan data diri dan pilih program yang diinginkan",
                  icon: FileText
                },
                { 
                  step: "02", 
                  title: "Wawancara Seleksi", 
                  desc: "Ikuti sesi wawancara dengan tim akademik untuk mengevaluasi kesiapan dan motivasi Anda",
                  icon: MessageCircle
                },
                { 
                  step: "03", 
                  title: "Pembayaran", 
                  desc: "Bayar biaya ujian sebesar Rp 300.000 melalui metode pembayaran yang tersedia",
                  icon: CreditCard
                },
                { 
                  step: "04", 
                  title: "Mulai Belajar", 
                  desc: "Bergabung dengan kelas dan mulai perjalanan transformasi kepemimpinan Anda",
                  icon: Play
                }
              ].map((item, index) => (
                <div key={index} className="flex items-start space-x-6">
                  <div className="flex-shrink-0">
                    <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-500 text-white rounded-2xl flex items-center justify-center text-xl font-bold shadow-lg">
                      {item.step}
                    </div>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center mb-3">
                      <item.icon className="h-6 w-6 text-yellow-600 mr-3" />
                      <h3 className="text-xl font-bold text-gray-900">{item.title}</h3>
                    </div>
                    <p className="text-gray-600 leading-relaxed">{item.desc}</p>
                  </div>
                </div>
              ))}
              
              <div className="pt-6">
                <Button 
                  size="lg" 
                  className="w-full bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-white text-lg px-8 py-4 font-semibold rounded-xl shadow-lg"
                  onClick={() => setShowRegistration(true)}
                >
                  Mulai Daftar Sekarang
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <RegistrationModal 
        isOpen={showRegistration} 
        onClose={() => setShowRegistration(false)} 
      />
      <AuthModal 
        isOpen={showAuth} 
        onClose={() => setShowAuth(false)} 
      />
    </div>
  );
};

export default LandingPage;
