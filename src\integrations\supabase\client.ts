
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://grdcpluwqfytturlbpjl.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdyZGNwbHV3cWZ5dHR1cmxicGpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAzODkxNjksImV4cCI6MjA2NTk2NTE2OX0.XXwAE8cL45SuNuQjo-vINn56WlNy1-3fOn7FVGzKfwg";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});
