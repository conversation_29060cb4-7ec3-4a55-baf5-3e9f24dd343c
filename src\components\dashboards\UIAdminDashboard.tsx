
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Users, DollarSign, FileText, BarChart, Building } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

interface ParticipantData {
  user_id: string;
  full_name: string;
  email: string;
  phone: string;
  program: string;
  current_stage: string;
  stage_status: string;
  registration_date: string | null;
  interview_date: string | null;
  account_created_date: string | null;
  exam_payment_date: string | null;
  exam_scheduled_date: string | null;
  reregistration_payment_date: string | null;
  active_participant_date: string | null;
  notes: string | null;
  company: string;
  position: string;
}

const UIAdminDashboard = () => {
  const [participants, setParticipants] = useState<ParticipantData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAllData();
  }, []);

  const fetchAllData = async () => {
    try {
      setLoading(true);
      console.log('Fetching all data for UI Admin...');
      
      // Get all user profiles
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('*');

      if (profilesError) {
        console.error('Error fetching profiles:', profilesError);
        return;
      }

      console.log('Profiles data:', profilesData);

      // Get all progress tracking data
      const { data: progressData, error: progressError } = await supabase
        .from('progress_tracking')
        .select('*');

      if (progressError) {
        console.error('Error fetching progress tracking:', progressError);
      }

      console.log('Progress tracking data:', progressData);

      // Get all program registrations
      const { data: registrationData, error: registrationError } = await supabase
        .from('program_registrations')
        .select('*');

      if (registrationError) {
        console.error('Error fetching program registrations:', registrationError);
      }

      console.log('Program registrations data:', registrationData);

      // Combine the data
      const formattedData = profilesData?.map(profile => {
        const progressRecord = progressData?.find(p => p.user_id === profile.id);
        const registrationRecord = registrationData?.find(r => r.user_id === profile.id);
        
        return {
          user_id: profile.id,
          full_name: profile.full_name || 'Tidak tersedia',
          email: profile.email || '',
          phone: profile.phone || 'Tidak tersedia',
          program: registrationRecord?.program || 'Tidak tersedia',
          company: registrationRecord?.company || 'Tidak tersedia',
          position: registrationRecord?.position || 'Tidak tersedia',
          current_stage: progressRecord?.current_stage || 'registration',
          stage_status: progressRecord?.stage_status || 'pending',
          registration_date: progressRecord?.registration_date || null,
          interview_date: progressRecord?.interview_date || null,
          account_created_date: progressRecord?.account_created_date || null,
          exam_payment_date: progressRecord?.exam_payment_date || null,
          exam_scheduled_date: progressRecord?.exam_scheduled_date || null,
          reregistration_payment_date: progressRecord?.reregistration_payment_date || null,
          active_participant_date: progressRecord?.active_participant_date || null,
          notes: progressRecord?.notes || null
        };
      }) || [];

      console.log('Formatted participants data for UI Admin:', formattedData);
      setParticipants(formattedData);
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStageTitle = (stage: string) => {
    const stageMap: { [key: string]: string } = {
      'registration': 'Pendaftaran',
      'interview': 'Wawancara',
      'account_created': 'Akun Dibuat',
      'exam_payment': 'Pembayaran Ujian',
      'exam_scheduled': 'Ujian Terjadwal',
      'reregistration_payment': 'Pembayaran Ulang',
      'active_participant': 'Peserta Aktif'
    };
    return stageMap[stage] || stage;
  };

  // Calculate stats from real data
  const totalParticipants = participants.length;
  const activeParticipants = participants.filter(p => p.current_stage === 'active_participant').length;
  const totalRevenue = participants.filter(p => p.exam_payment_date || p.reregistration_payment_date).length * 300000; // Asumsi biaya 300k per peserta
  const pendingPayments = participants.filter(p => 
    (p.current_stage === 'exam_payment' && !p.exam_payment_date) ||
    (p.current_stage === 'reregistration_payment' && !p.reregistration_payment_date)
  ).length;

  // Group participants by stage for funnel
  const leadsList = participants.filter(p => p.current_stage === 'registration' || p.current_stage === 'interview');
  const examList = participants.filter(p => p.current_stage === 'exam_payment' || p.current_stage === 'exam_scheduled');
  const reregistrationList = participants.filter(p => 
    p.current_stage === 'reregistration_payment' || p.current_stage === 'active_participant'
  );

  // Group by program for financial summary
  const programSummary = participants.reduce((acc, participant) => {
    const program = participant.program;
    if (!acc[program]) {
      acc[program] = {
        count: 0,
        revenue: 0,
        pendingPayments: 0
      };
    }
    acc[program].count++;
    if (participant.exam_payment_date || participant.reregistration_payment_date) {
      acc[program].revenue += 300000;
    }
    if ((participant.current_stage === 'exam_payment' && !participant.exam_payment_date) ||
        (participant.current_stage === 'reregistration_payment' && !participant.reregistration_payment_date)) {
      acc[program].pendingPayments++;
    }
    return acc;
  }, {} as Record<string, { count: number; revenue: number; pendingPayments: number }>);

  const stats = [
    { label: 'Total Program', value: Object.keys(programSummary).length.toString(), icon: Building, color: 'text-blue-600' },
    { label: 'Peserta Aktif', value: activeParticipants.toString(), icon: Users, color: 'text-green-600' },
    { label: 'Total Pendapatan', value: `Rp ${(totalRevenue / 1000000).toFixed(1)}M`, icon: BarChart, color: 'text-purple-600' },
    { label: 'Pembayaran Tertunda', value: pendingPayments.toString(), icon: DollarSign, color: 'text-orange-600' }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Memuat data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard Admin UI</h1>
          <p className="text-gray-600">Ringkasan program CSEL UI dan kinerja keuangan (Hanya Lihat)</p>
          <div className="mt-2">
            <Button 
              onClick={fetchAllData} 
              variant="outline" 
              size="sm"
            >
              Refresh Data
            </Button>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid md:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                    <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                  <stat.icon className={`h-8 w-8 ${stat.color}`} />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Participants Summary */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Ringkasan Data Peserta</CardTitle>
            <CardDescription>Total: {participants.length} peserta terdaftar</CardDescription>
          </CardHeader>
          <CardContent>
            {participants.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">Tidak ada data peserta ditemukan</p>
              </div>
            ) : (
              <div className="grid md:grid-cols-3 gap-6">
                <div className="bg-blue-50 p-4 rounded-lg text-center">
                  <h3 className="font-semibold text-blue-900 mb-2">Leads</h3>
                  <p className="text-3xl font-bold text-blue-700">{leadsList.length}</p>
                  <p className="text-sm text-blue-600">Calon peserta baru</p>
                </div>
                <div className="bg-yellow-50 p-4 rounded-lg text-center">
                  <h3 className="font-semibold text-yellow-900 mb-2">Ujian</h3>
                  <p className="text-3xl font-bold text-yellow-700">{examList.length}</p>
                  <p className="text-sm text-yellow-600">Terdaftar ujian</p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg text-center">
                  <h3 className="font-semibold text-green-900 mb-2">Daftar Ulang</h3>
                  <p className="text-3xl font-bold text-green-700">{reregistrationList.length}</p>
                  <p className="text-sm text-green-600">Peserta aktif</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Main Content Tabs */}
        <Tabs defaultValue="leads" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="leads">Leads</TabsTrigger>
            <TabsTrigger value="ujian">Ujian</TabsTrigger>
            <TabsTrigger value="daftar-ulang">Daftar Ulang</TabsTrigger>
            <TabsTrigger value="keuangan">Ringkasan Keuangan</TabsTrigger>
          </TabsList>

          <TabsContent value="leads">
            <Card>
              <CardHeader>
                <CardTitle>Data Leads ({leadsList.length})</CardTitle>
                <CardDescription>Calon peserta yang baru mendaftar</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nama</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Program</TableHead>
                      <TableHead>Tanggal Daftar</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {leadsList.map((lead) => (
                      <TableRow key={lead.user_id}>
                        <TableCell className="font-medium">{lead.full_name}</TableCell>
                        <TableCell>{lead.email}</TableCell>
                        <TableCell>{lead.program}</TableCell>
                        <TableCell>
                          {lead.registration_date ? new Date(lead.registration_date).toLocaleDateString('id-ID') : 'TBD'}
                        </TableCell>
                        <TableCell>
                          <Badge className="bg-blue-100 text-blue-800">
                            {getStageTitle(lead.current_stage)}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="ujian">
            <Card>
              <CardHeader>
                <CardTitle>Data Ujian ({examList.length})</CardTitle>
                <CardDescription>Peserta yang terdaftar untuk ujian</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nama</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Program</TableHead>
                      <TableHead>Tanggal Ujian</TableHead>
                      <TableHead>Pembayaran</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {examList.map((exam) => (
                      <TableRow key={exam.user_id}>
                        <TableCell className="font-medium">{exam.full_name}</TableCell>
                        <TableCell>{exam.email}</TableCell>
                        <TableCell>{exam.program}</TableCell>
                        <TableCell>
                          {exam.exam_scheduled_date ? new Date(exam.exam_scheduled_date).toLocaleDateString('id-ID') : 'TBD'}
                        </TableCell>
                        <TableCell>
                          <Badge className={
                            exam.exam_payment_date ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'
                          }>
                            {exam.exam_payment_date ? 'Sudah Bayar' : 'Belum Bayar'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className="bg-yellow-100 text-yellow-800">
                            {getStageTitle(exam.current_stage)}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="daftar-ulang">
            <Card>
              <CardHeader>
                <CardTitle>Data Daftar Ulang ({reregistrationList.length})</CardTitle>
                <CardDescription>Peserta yang sudah lulus ujian dan melakukan daftar ulang</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nama</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Program</TableHead>
                      <TableHead>Tanggal Daftar Ulang</TableHead>
                      <TableHead>Status Pembayaran</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {reregistrationList.map((participant) => (
                      <TableRow key={participant.user_id}>
                        <TableCell className="font-medium">{participant.full_name}</TableCell>
                        <TableCell>{participant.email}</TableCell>
                        <TableCell>{participant.program}</TableCell>
                        <TableCell>
                          {participant.reregistration_payment_date ? 
                            new Date(participant.reregistration_payment_date).toLocaleDateString('id-ID') : 'TBD'}
                        </TableCell>
                        <TableCell>
                          <Badge className={
                            participant.reregistration_payment_date ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'
                          }>
                            {participant.reregistration_payment_date ? 'Sudah Bayar' : 'Menunggu Pembayaran'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={
                            participant.current_stage === 'active_participant' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                          }>
                            {getStageTitle(participant.current_stage)}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="keuangan">
            <Card>
              <CardHeader>
                <CardTitle>Ringkasan Keuangan</CardTitle>
                <CardDescription>Pelacakan pendapatan dan ringkasan keuangan</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="grid md:grid-cols-3 gap-4">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h3 className="font-medium text-blue-900">Pendapatan Bulanan</h3>
                      <p className="text-2xl font-bold text-blue-700">
                        Rp {((totalRevenue * 0.1) / 1000000).toFixed(1)}M
                      </p>
                      <p className="text-sm text-blue-600">Bulan ini</p>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <h3 className="font-medium text-green-900">Total Pendapatan</h3>
                      <p className="text-2xl font-bold text-green-700">
                        Rp {(totalRevenue / 1000000).toFixed(1)}M
                      </p>
                      <p className="text-sm text-green-600">Sepanjang waktu</p>
                    </div>
                    <div className="bg-orange-50 p-4 rounded-lg">
                      <h3 className="font-medium text-orange-900">Tertunda</h3>
                      <p className="text-2xl font-bold text-orange-700">
                        Rp {((pendingPayments * 300000) / 1000000).toFixed(1)}M
                      </p>
                      <p className="text-sm text-orange-600">{pendingPayments} pembayaran</p>
                    </div>
                  </div>

                  {Object.keys(programSummary).length > 0 && (
                    <div className="grid md:grid-cols-2 gap-6">
                      {Object.entries(programSummary).map(([program, data], index) => (
                        <div key={index} className="border rounded-lg p-4">
                          <h3 className="font-semibold text-lg mb-3">{program}</h3>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-600">Peserta:</span>
                              <span className="font-medium">{data.count}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Total Pendapatan:</span>
                              <span className="font-medium text-green-600">
                                Rp {(data.revenue / 1000000).toFixed(1)}M
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Bulan Ini:</span>
                              <span className="font-medium text-blue-600">
                                Rp {((data.revenue * 0.1) / 1000000).toFixed(1)}M
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Pembayaran Tertunda:</span>
                              <span className="font-medium text-orange-600">{data.pendingPayments}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-sm text-gray-600 italic">
                      Catatan: Ini adalah dashboard hanya untuk melihat. Untuk manajemen pembayaran dan administrasi peserta, 
                      silakan hubungi tim Admin MCU.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default UIAdminDashboard;
