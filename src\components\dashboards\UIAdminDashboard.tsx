
import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Users, DollarSign, FileText, BarChart, Building, RefreshCw } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useDataCache } from '@/hooks/useDataCache';
import { AdminDashboardSkeleton, UIAdminTabsSkeleton } from '@/components/ui/dashboard-skeleton';

interface ParticipantData {
  user_id: string;
  full_name: string;
  email: string;
  phone: string;
  program: string;
  current_stage: string;
  stage_status: string;
  registration_date: string | null;
  interview_date: string | null;
  account_created_date: string | null;
  exam_payment_date: string | null;
  exam_scheduled_date: string | null;
  reregistration_payment_date: string | null;
  active_participant_date: string | null;
  notes: string | null;
  company: string;
  position: string;
}

const UIAdminDashboard = () => {
  // Use cached data fetching with single optimized query
  const fetchUIAdminData = useCallback(async () => {
    console.log('Fetching UI admin data...');

    try {
      // Single query with joins to get all participant data at once
      const { data, error } = await supabase
        .from('profiles')
        .select(`
          id,
          full_name,
          email,
          phone,
          user_roles!inner(role),
          program_registrations!inner(
            program,
            company,
            position,
            created_at
          ),
          progress_tracking(
            current_stage,
            stage_status,
            registration_date,
            interview_date,
            account_created_date,
            exam_payment_date,
            exam_scheduled_date,
            reregistration_payment_date,
            active_participant_date,
            notes
          )
        `)
        .eq('user_roles.role', 'participant');

      if (error) {
        console.error('Error fetching UI admin data:', error);
        throw new Error('Gagal memuat data peserta');
      }

      console.log('Raw UI admin data:', data);

      // Format the data
      const formattedData = data?.map(participant => {
        const progressRecord = participant.progress_tracking?.[0];
        const registrationRecord = participant.program_registrations?.[0];

        return {
          user_id: participant.id,
          full_name: participant.full_name || 'Tidak tersedia',
          email: participant.email || '',
          phone: participant.phone || 'Tidak tersedia',
          program: registrationRecord?.program || 'Tidak tersedia',
          company: registrationRecord?.company || 'Tidak tersedia',
          position: registrationRecord?.position || 'Tidak tersedia',
          current_stage: progressRecord?.current_stage || 'registration',
          stage_status: progressRecord?.stage_status || 'pending',
          registration_date: progressRecord?.registration_date || null,
          interview_date: progressRecord?.interview_date || null,
          account_created_date: progressRecord?.account_created_date || null,
          exam_payment_date: progressRecord?.exam_payment_date || null,
          exam_scheduled_date: progressRecord?.exam_scheduled_date || null,
          reregistration_payment_date: progressRecord?.reregistration_payment_date || null,
          active_participant_date: progressRecord?.active_participant_date || null,
          notes: progressRecord?.notes || null
        };
      }) || [];

      console.log('Formatted UI admin data:', formattedData);
      console.log('Total participants found for UI admin:', formattedData.length);

      return formattedData;
    } catch (error) {
      console.error('Error in fetchUIAdminData:', error);
      throw error;
    }
  }, []);

  const {
    data: participants = [],
    loading,
    error,
    isStale,
    refresh
  } = useDataCache('ui-admin-participants', fetchUIAdminData, {
    ttl: 30 * 1000, // 30 seconds cache for faster updates
    staleWhileRevalidate: true
  });



  const getStageTitle = (stage: string) => {
    const stageMap: { [key: string]: string } = {
      'registration': 'Pendaftaran',
      'interview': 'Wawancara',
      'account_created': 'Akun Dibuat',
      'exam_payment': 'Pembayaran Ujian',
      'exam_scheduled': 'Ujian Terjadwal',
      'reregistration_payment': 'Pembayaran Ulang',
      'active_participant': 'Peserta Aktif'
    };
    return stageMap[stage] || stage;
  };

  // Ensure participants is always an array
  const safeParticipants = participants || [];

  // Calculate stats from real data
  const totalParticipants = safeParticipants.length;
  const activeParticipants = safeParticipants.filter(p => p.current_stage === 'active_participant').length;
  const totalRevenue = safeParticipants.filter(p => p.exam_payment_date || p.reregistration_payment_date).length * 300000; // Asumsi biaya 300k per peserta
  const pendingPayments = safeParticipants.filter(p =>
    (p.current_stage === 'exam_payment' && !p.exam_payment_date) ||
    (p.current_stage === 'reregistration_payment' && !p.reregistration_payment_date)
  ).length;

  // Group participants by stage for funnel
  const leadsList = safeParticipants.filter(p => p.current_stage === 'registration' || p.current_stage === 'interview');
  const examList = safeParticipants.filter(p => p.current_stage === 'exam_payment' || p.current_stage === 'exam_scheduled');
  const reregistrationList = safeParticipants.filter(p =>
    p.current_stage === 'reregistration_payment' || p.current_stage === 'active_participant'
  );

  // Group by program for financial summary
  const programSummary = safeParticipants.reduce((acc, participant) => {
    const program = participant.program;
    if (!acc[program]) {
      acc[program] = {
        count: 0,
        revenue: 0,
        pendingPayments: 0
      };
    }
    acc[program].count++;
    if (participant.exam_payment_date || participant.reregistration_payment_date) {
      acc[program].revenue += 300000;
    }
    if ((participant.current_stage === 'exam_payment' && !participant.exam_payment_date) ||
        (participant.current_stage === 'reregistration_payment' && !participant.reregistration_payment_date)) {
      acc[program].pendingPayments++;
    }
    return acc;
  }, {} as Record<string, { count: number; revenue: number; pendingPayments: number }>);

  const stats = [
    { label: 'Total Program', value: Object.keys(programSummary).length.toString(), icon: Building, color: 'text-blue-600' },
    { label: 'Peserta Aktif', value: activeParticipants.toString(), icon: Users, color: 'text-green-600' },
    { label: 'Total Pendapatan', value: `Rp ${(totalRevenue / 1000000).toFixed(1)}M`, icon: BarChart, color: 'text-purple-600' },
    { label: 'Pembayaran Tertunda', value: pendingPayments.toString(), icon: DollarSign, color: 'text-orange-600' }
  ];

  if (loading && !safeParticipants.length) {
    return <AdminDashboardSkeleton />;
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard Admin UI</h1>
          <p className="text-gray-600">Ringkasan program CSEL UI dan kinerja keuangan (Hanya Lihat)</p>
          <div className="mt-2 flex items-center gap-2">
            <Button
              onClick={refresh}
              variant="outline"
              size="sm"
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
              Refresh Data
            </Button>
            {isStale && (
              <Badge variant="outline" className="text-orange-600">
                Data mungkin tidak terbaru
              </Badge>
            )}
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid md:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                    <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                  <stat.icon className={`h-8 w-8 ${stat.color}`} />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Participants Summary */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Ringkasan Data Peserta</CardTitle>
            <CardDescription>Total: {safeParticipants.length} peserta terdaftar</CardDescription>
          </CardHeader>
          <CardContent>
            {safeParticipants.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">Tidak ada data peserta ditemukan</p>
              </div>
            ) : (
              <div className="grid md:grid-cols-3 gap-6">
                <div className="bg-blue-50 p-4 rounded-lg text-center">
                  <h3 className="font-semibold text-blue-900 mb-2">Leads</h3>
                  <p className="text-3xl font-bold text-blue-700">{leadsList.length}</p>
                  <p className="text-sm text-blue-600">Calon peserta baru</p>
                </div>
                <div className="bg-yellow-50 p-4 rounded-lg text-center">
                  <h3 className="font-semibold text-yellow-900 mb-2">Ujian</h3>
                  <p className="text-3xl font-bold text-yellow-700">{examList.length}</p>
                  <p className="text-sm text-yellow-600">Terdaftar ujian</p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg text-center">
                  <h3 className="font-semibold text-green-900 mb-2">Daftar Ulang</h3>
                  <p className="text-3xl font-bold text-green-700">{reregistrationList.length}</p>
                  <p className="text-sm text-green-600">Peserta aktif</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Main Content Tabs */}
        <Tabs defaultValue="leads" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="leads">Leads</TabsTrigger>
            <TabsTrigger value="ujian">Ujian</TabsTrigger>
            <TabsTrigger value="daftar-ulang">Daftar Ulang</TabsTrigger>
            <TabsTrigger value="keuangan">Ringkasan Keuangan</TabsTrigger>
          </TabsList>

          <TabsContent value="leads">
            <Card>
              <CardHeader>
                <CardTitle>Data Leads ({leadsList.length})</CardTitle>
                <CardDescription>Calon peserta yang baru mendaftar</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nama</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Program</TableHead>
                      <TableHead>Tanggal Daftar</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {leadsList.map((lead) => (
                      <TableRow key={lead.user_id}>
                        <TableCell className="font-medium">{lead.full_name}</TableCell>
                        <TableCell>{lead.email}</TableCell>
                        <TableCell>{lead.program}</TableCell>
                        <TableCell>
                          {lead.registration_date ? new Date(lead.registration_date).toLocaleDateString('id-ID') : 'TBD'}
                        </TableCell>
                        <TableCell>
                          <Badge className="bg-blue-100 text-blue-800">
                            {getStageTitle(lead.current_stage)}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="ujian">
            <Card>
              <CardHeader>
                <CardTitle>Data Ujian ({examList.length})</CardTitle>
                <CardDescription>Peserta yang terdaftar untuk ujian</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nama</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Program</TableHead>
                      <TableHead>Tanggal Ujian</TableHead>
                      <TableHead>Pembayaran</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {examList.map((exam) => (
                      <TableRow key={exam.user_id}>
                        <TableCell className="font-medium">{exam.full_name}</TableCell>
                        <TableCell>{exam.email}</TableCell>
                        <TableCell>{exam.program}</TableCell>
                        <TableCell>
                          {exam.exam_scheduled_date ? new Date(exam.exam_scheduled_date).toLocaleDateString('id-ID') : 'TBD'}
                        </TableCell>
                        <TableCell>
                          <Badge className={
                            exam.exam_payment_date ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'
                          }>
                            {exam.exam_payment_date ? 'Sudah Bayar' : 'Belum Bayar'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className="bg-yellow-100 text-yellow-800">
                            {getStageTitle(exam.current_stage)}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="daftar-ulang">
            <Card>
              <CardHeader>
                <CardTitle>Data Daftar Ulang ({reregistrationList.length})</CardTitle>
                <CardDescription>Peserta yang sudah lulus ujian dan melakukan daftar ulang</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nama</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Program</TableHead>
                      <TableHead>Tanggal Daftar Ulang</TableHead>
                      <TableHead>Status Pembayaran</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {reregistrationList.map((participant) => (
                      <TableRow key={participant.user_id}>
                        <TableCell className="font-medium">{participant.full_name}</TableCell>
                        <TableCell>{participant.email}</TableCell>
                        <TableCell>{participant.program}</TableCell>
                        <TableCell>
                          {participant.reregistration_payment_date ? 
                            new Date(participant.reregistration_payment_date).toLocaleDateString('id-ID') : 'TBD'}
                        </TableCell>
                        <TableCell>
                          <Badge className={
                            participant.reregistration_payment_date ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'
                          }>
                            {participant.reregistration_payment_date ? 'Sudah Bayar' : 'Menunggu Pembayaran'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={
                            participant.current_stage === 'active_participant' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                          }>
                            {getStageTitle(participant.current_stage)}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="keuangan">
            <Card>
              <CardHeader>
                <CardTitle>Ringkasan Keuangan</CardTitle>
                <CardDescription>Pelacakan pendapatan dan ringkasan keuangan</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="grid md:grid-cols-3 gap-4">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h3 className="font-medium text-blue-900">Pendapatan Bulanan</h3>
                      <p className="text-2xl font-bold text-blue-700">
                        Rp {((totalRevenue * 0.1) / 1000000).toFixed(1)}M
                      </p>
                      <p className="text-sm text-blue-600">Bulan ini</p>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <h3 className="font-medium text-green-900">Total Pendapatan</h3>
                      <p className="text-2xl font-bold text-green-700">
                        Rp {(totalRevenue / 1000000).toFixed(1)}M
                      </p>
                      <p className="text-sm text-green-600">Sepanjang waktu</p>
                    </div>
                    <div className="bg-orange-50 p-4 rounded-lg">
                      <h3 className="font-medium text-orange-900">Tertunda</h3>
                      <p className="text-2xl font-bold text-orange-700">
                        Rp {((pendingPayments * 300000) / 1000000).toFixed(1)}M
                      </p>
                      <p className="text-sm text-orange-600">{pendingPayments} pembayaran</p>
                    </div>
                  </div>

                  {Object.keys(programSummary).length > 0 && (
                    <div className="grid md:grid-cols-2 gap-6">
                      {Object.entries(programSummary).map(([program, data], index) => (
                        <div key={index} className="border rounded-lg p-4">
                          <h3 className="font-semibold text-lg mb-3">{program}</h3>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-600">Peserta:</span>
                              <span className="font-medium">{data.count}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Total Pendapatan:</span>
                              <span className="font-medium text-green-600">
                                Rp {(data.revenue / 1000000).toFixed(1)}M
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Bulan Ini:</span>
                              <span className="font-medium text-blue-600">
                                Rp {((data.revenue * 0.1) / 1000000).toFixed(1)}M
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Pembayaran Tertunda:</span>
                              <span className="font-medium text-orange-600">{data.pendingPayments}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-sm text-gray-600 italic">
                      Catatan: Ini adalah dashboard hanya untuk melihat. Untuk manajemen pembayaran dan administrasi peserta, 
                      silakan hubungi tim Admin MCU.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default UIAdminDashboard;
