
-- Create user roles table
CREATE TABLE public.user_roles (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  role TEXT NOT NULL CHECK (role IN ('participant', 'mcu_admin', 'ui_admin')),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(user_id)
);

-- Enable RLS on user_roles
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;

-- Create policies for user_roles
CREATE POLICY "Users can view own role" ON public.user_roles
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Ad<PERSON> can view all roles" ON public.user_roles
  FOR SELECT USING (true);

CREATE POLICY "Admins can manage roles" ON public.user_roles
  FOR ALL USING (true);

-- Insert user roles untuk user yang sudah dibuat
-- Peserta
INSERT INTO public.user_roles (user_id, role) 
SELECT id, 'participant' 
FROM public.profiles 
WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>')
ON CONFLICT (user_id) DO UPDATE SET role = 'participant';

-- MCU admin
INSERT INTO public.user_roles (user_id, role) 
SELECT id, 'mcu_admin' 
FROM public.profiles 
WHERE email = '<EMAIL>'
ON CONFLICT (user_id) DO UPDATE SET role = 'mcu_admin';

-- UI admin
INSERT INTO public.user_roles (user_id, role) 
SELECT id, 'ui_admin' 
FROM public.profiles 
WHERE email = '<EMAIL>'
ON CONFLICT (user_id) DO UPDATE SET role = 'ui_admin';
