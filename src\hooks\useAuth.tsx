
import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { User, Session } from '@supabase/supabase-js';

interface AuthUser {
  id: string;
  email: string;
  name: string;
  role: 'participant' | 'mcu_admin' | 'ui_admin';
  status?: string;
}

interface AuthContextType {
  user: AuthUser | null;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  register: (userData: any) => Promise<void>;
  signUp: (email: string, password: string, fullName: string) => Promise<{ error: any }>;
  session: Session | null;
  loading: boolean;
  error: string | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUserRole = async (userId: string) => {
    try {
      console.log('Fetching role for user:', userId);
      
      // Add timeout untuk mencegah hanging
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Role fetch timeout')), 10000)
      );
      
      const rolePromise = supabase
        .from('user_roles')
        .select('role')
        .eq('user_id', userId)
        .single();

      const { data, error } = await Promise.race([rolePromise, timeoutPromise]) as any;

      if (error) {
        console.log('No role found for user, defaulting to participant. Error:', error);
        return 'participant';
      }

      console.log('Role found:', data?.role);
      return data?.role || 'participant';
    } catch (error) {
      console.error('Error fetching user role:', error);
      return 'participant';
    }
  };

  const updateUserWithRole = async (authUser: User) => {
    try {
      console.log('Updating user with role for:', authUser.email);
      setError(null);
      
      const role = await fetchUserRole(authUser.id);
      
      const authUserWithRole: AuthUser = {
        id: authUser.id,
        email: authUser.email || '',
        name: authUser.user_metadata?.full_name || authUser.email || '',
        role: role as 'participant' | 'mcu_admin' | 'ui_admin'
      };
      
      console.log('Setting user with role:', authUserWithRole);
      setUser(authUserWithRole);
      setIsAuthenticated(true);
    } catch (error) {
      console.error('Error updating user with role:', error);
      setError('Failed to load user data');
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log('Setting up auth state listener');
    
    let isMounted = true;
    
    // Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email);
        
        if (!isMounted) return;
        
        setSession(session);
        
        if (session?.user) {
          await updateUserWithRole(session.user);
        } else {
          setUser(null);
          setIsAuthenticated(false);
          setLoading(false);
        }
      }
    );

    // Check for existing session dengan timeout
    const getInitialSession = async () => {
      try {
        console.log('Checking for existing session');
        
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Session check timeout')), 10000)
        );
        
        const sessionPromise = supabase.auth.getSession();
        
        const { data: { session }, error } = await Promise.race([sessionPromise, timeoutPromise]) as any;
        
        if (!isMounted) return;
        
        if (error) {
          console.error('Error getting session:', error);
          setError('Failed to check authentication');
          setLoading(false);
          return;
        }

        setSession(session);
        if (session?.user) {
          await updateUserWithRole(session.user);
        } else {
          setLoading(false);
        }
      } catch (error) {
        console.error('Error in getInitialSession:', error);
        if (isMounted) {
          setError('Authentication check failed');
          setLoading(false);
        }
      }
    };

    getInitialSession();

    return () => {
      isMounted = false;
      console.log('Cleaning up auth subscription');
      subscription.unsubscribe();
    };
  }, []);

  const signUp = async (email: string, password: string, fullName: string) => {
    try {
      setLoading(true);
      setError(null);
      const redirectUrl = `${window.location.origin}/`;
      
      console.log('Attempting signup for:', email);
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: redirectUrl,
          data: {
            full_name: fullName
          }
        }
      });
      
      return { error };
    } catch (error) {
      console.error('Signup error:', error);
      setError('Signup failed');
      return { error };
    } finally {
      setLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    try {
      setLoading(true);
      setError(null);
      console.log('Attempting login with:', email);
      
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      
      if (error) {
        console.error('Login error:', error);
        setError(error.message);
        throw new Error(error.message);
      }
      
      console.log('Login successful');
    } catch (error) {
      console.error('Login failed:', error);
      setError('Login failed');
      throw error;
    }
  };

  const logout = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('Logging out');
      await supabase.auth.signOut();
      setUser(null);
      setSession(null);
      setIsAuthenticated(false);
    } catch (error) {
      console.error('Logout error:', error);
      setError('Logout failed');
    } finally {
      setLoading(false);
    }
  };

  const register = async (userData: any) => {
    if (!session?.user) {
      throw new Error('User must be authenticated to register');
    }

    console.log('Registering user data for:', session.user.email);
    const { error } = await supabase
      .from('program_registrations')
      .insert({
        user_id: session.user.id,
        full_name: userData.fullName,
        email: userData.email,
        phone: userData.phone,
        program: userData.program,
        company: userData.company,
        position: userData.position,
        experience: userData.experience,
        motivation: userData.motivation
      });

    if (error) {
      console.error('Registration error:', error);
      throw new Error(error.message);
    }
  };

  const contextValue: AuthContextType = {
    user,
    isAuthenticated,
    login,
    logout,
    register,
    signUp,
    session,
    loading,
    error
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
