
import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { User, Session } from '@supabase/supabase-js';

interface AuthUser {
  id: string;
  email: string;
  name: string;
  role: 'participant' | 'mcu_admin' | 'ui_admin';
  status?: string;
}

interface AuthContextType {
  user: AuthUser | null;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  register: (userData: any) => Promise<void>;
  signUp: (email: string, password: string, fullName: string) => Promise<{ error: any }>;
  session: Session | null;
  loading: boolean;
  error: string | null;
  refreshUserRole: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Cache for user roles to avoid repeated fetches
  const roleCache = new Map<string, string>();

  const fetchUserRole = async (userId: string, forceRefresh = false) => {
    try {
      console.log('=== FETCHING USER ROLE ===');
      console.log('User ID:', userId);
      console.log('Force refresh:', forceRefresh);

      // Check cache first (unless force refresh)
      if (!forceRefresh && roleCache.has(userId)) {
        const cachedRole = roleCache.get(userId);
        console.log('Role found in cache:', cachedRole);
        return cachedRole;
      }

      console.log('Querying database for role...');

      // Try multiple approaches to get the role
      let role = null;
      let error = null;

      // Approach 1: Direct query with timeout
      try {
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Role fetch timeout')), 3000)
        );

        const rolePromise = supabase
          .from('user_roles')
          .select('role')
          .eq('user_id', userId)
          .single();

        const result = await Promise.race([rolePromise, timeoutPromise]) as any;

        console.log('Direct query result:', result);

        if (result.data && !result.error) {
          role = result.data.role;
          console.log('Role found via direct query:', role);
        } else {
          error = result.error;
          console.log('Direct query failed:', error);
        }
      } catch (directError) {
        console.log('Direct query threw error:', directError);
        error = directError;
      }

      // Approach 2: If direct query failed, try with join
      if (!role) {
        try {
          console.log('Trying with JOIN query...');
          const joinResult = await supabase
            .from('user_roles')
            .select(`
              role,
              profiles!inner(email)
            `)
            .eq('user_id', userId)
            .single();

          console.log('JOIN query result:', joinResult);

          if (joinResult.data && !joinResult.error) {
            role = joinResult.data.role;
            console.log('Role found via JOIN query:', role);
          }
        } catch (joinError) {
          console.log('JOIN query failed:', joinError);
        }
      }

      // Approach 3: Hardcoded mapping as fallback
      if (!role) {
        console.log('Using hardcoded mapping fallback...');
        // Get user email from session or profiles
        try {
          const { data: profileData } = await supabase
            .from('profiles')
            .select('email')
            .eq('id', userId)
            .single();

          const email = profileData?.email;
          console.log('User email for fallback:', email);

          // Hardcoded role mapping
          const roleMapping: { [key: string]: string } = {
            '<EMAIL>': 'ui_admin',
            '<EMAIL>': 'mcu_admin'
          };

          if (email && roleMapping[email]) {
            role = roleMapping[email];
            console.log('Role found via hardcoded mapping:', role);
          }
        } catch (profileError) {
          console.log('Profile query failed:', profileError);
        }
      }

      // Final fallback
      if (!role) {
        console.log('All approaches failed, defaulting to participant');
        role = 'participant';
      }

      console.log('Final role determined:', role);
      roleCache.set(userId, role);
      console.log('=========================');
      return role;

    } catch (error) {
      console.error('Critical error in fetchUserRole:', error);
      const defaultRole = 'participant';
      roleCache.set(userId, defaultRole);
      return defaultRole;
    }
  };

  const updateUserWithRole = async (authUser: User, forceRefresh = false) => {
    try {
      console.log('=== UPDATING USER WITH ROLE ===');
      console.log('User email:', authUser.email);
      console.log('Force refresh:', forceRefresh);
      setError(null);

      // Quick role determination for known admin emails
      let quickRole = null;
      const email = authUser.email;
      if (email === '<EMAIL>') {
        quickRole = 'ui_admin';
        console.log('Quick role assignment for UI admin');
      } else if (email === '<EMAIL>') {
        quickRole = 'mcu_admin';
        console.log('Quick role assignment for MCU admin');
      }

      // Use quick role if available and not forcing refresh
      const role = (quickRole && !forceRefresh) ? quickRole : await fetchUserRole(authUser.id, forceRefresh);

      const authUserWithRole: AuthUser = {
        id: authUser.id,
        email: authUser.email || '',
        name: authUser.user_metadata?.full_name || authUser.email || '',
        role: role as 'participant' | 'mcu_admin' | 'ui_admin'
      };

      console.log('Final user object:', authUserWithRole);
      console.log('Dashboard should route to:',
        role === 'mcu_admin' ? 'MCU Admin Dashboard' :
        role === 'ui_admin' ? 'UI Admin Dashboard' :
        'Participant Dashboard'
      );
      console.log('===============================');

      setUser(authUserWithRole);
      setIsAuthenticated(true);
    } catch (error) {
      console.error('Error updating user with role:', error);
      setError('Failed to load user data');
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log('Setting up auth state listener');
    
    let isMounted = true;
    
    // Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email);
        
        if (!isMounted) return;
        
        setSession(session);
        
        if (session?.user) {
          // Clear cache on auth state change to ensure fresh data
          if (event === 'SIGNED_IN') {
            roleCache.clear();
            // Clear global cache dynamically to avoid circular dependency
            try {
              import('./useDataCache').then(({ globalCache }) => {
                globalCache.clear();
              });
            } catch (error) {
              console.log('Could not clear global cache:', error);
            }
          }
          await updateUserWithRole(session.user);
        } else {
          setUser(null);
          setIsAuthenticated(false);
          setLoading(false);
        }
      }
    );

    // Check for existing session dengan timeout
    const getInitialSession = async () => {
      try {
        console.log('Checking for existing session');
        
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Session check timeout')), 3000) // Further reduced timeout
        );
        
        const sessionPromise = supabase.auth.getSession();
        
        const { data: { session }, error } = await Promise.race([sessionPromise, timeoutPromise]) as any;
        
        if (!isMounted) return;
        
        if (error) {
          console.error('Error getting session:', error);
          setError('Failed to check authentication');
          setLoading(false);
          return;
        }

        setSession(session);
        if (session?.user) {
          await updateUserWithRole(session.user);
        } else {
          setLoading(false);
        }
      } catch (error) {
        console.error('Error in getInitialSession:', error);
        if (isMounted) {
          setError('Authentication check failed');
          setLoading(false);
        }
      }
    };

    getInitialSession();

    return () => {
      isMounted = false;
      console.log('Cleaning up auth subscription');
      subscription.unsubscribe();
    };
  }, []);

  const signUp = async (email: string, password: string, fullName: string) => {
    try {
      setLoading(true);
      setError(null);
      const redirectUrl = `${window.location.origin}/`;
      
      console.log('Attempting signup for:', email);
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: redirectUrl,
          data: {
            full_name: fullName
          }
        }
      });
      
      return { error };
    } catch (error) {
      console.error('Signup error:', error);
      setError('Signup failed');
      return { error };
    } finally {
      setLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    try {
      setLoading(true);
      setError(null);
      console.log('Attempting login with:', email);
      
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      
      if (error) {
        console.error('Login error:', error);
        setError(error.message);
        throw new Error(error.message);
      }
      
      console.log('Login successful');
    } catch (error) {
      console.error('Login failed:', error);
      setError('Login failed');
      throw error;
    }
  };

  const logout = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('Logging out');
      roleCache.clear(); // Clear role cache on logout
      await supabase.auth.signOut();
      setUser(null);
      setSession(null);
      setIsAuthenticated(false);
    } catch (error) {
      console.error('Logout error:', error);
      setError('Logout failed');
    } finally {
      setLoading(false);
    }
  };

  const register = async (userData: any) => {
    if (!session?.user) {
      throw new Error('User must be authenticated to register');
    }

    console.log('Registering user data for:', session.user.email);
    const { error } = await supabase
      .from('program_registrations')
      .insert({
        user_id: session.user.id,
        full_name: userData.fullName,
        email: userData.email,
        phone: userData.phone,
        program: userData.program,
        company: userData.company,
        position: userData.position,
        experience: userData.experience,
        motivation: userData.motivation
      });

    if (error) {
      console.error('Registration error:', error);
      throw new Error(error.message);
    }
  };

  const refreshUserRole = async () => {
    if (session?.user) {
      console.log('Refreshing user role...');
      roleCache.clear(); // Clear role cache
      // Clear all dashboard data cache - import dynamically to avoid circular dependency
      try {
        const { globalCache } = await import('./useDataCache');
        globalCache.clear();
      } catch (error) {
        console.log('Could not clear global cache:', error);
      }
      await updateUserWithRole(session.user, true); // Force refresh
    }
  };

  const contextValue: AuthContextType = {
    user,
    isAuthenticated,
    login,
    logout,
    register,
    signUp,
    session,
    loading,
    error,
    refreshUserRole
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
